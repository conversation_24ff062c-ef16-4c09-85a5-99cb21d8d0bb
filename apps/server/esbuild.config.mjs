import { build } from 'esbuild'
import { readFileSync, copyFileSync, mkdirSync } from 'fs'
import { join, dirname } from 'path'
// 读取 package.json 来自动获取依赖列表
const pkg = JSON.parse(readFileSync('./package.json', 'utf8'))

const dependencies = Object.keys(pkg.dependencies || {})
const external = dependencies.filter((dep) => !dep.startsWith('@coozf'))

// 构建应用
await build({
  entryPoints: {
    index: 'src/index.ts',
    admin: 'src/admin.ts',
  },
  bundle: true,
  platform: 'node',
  target: 'node18',
  format: 'cjs',
  outdir: 'dist',
  sourcemap: true,
  external: external,
  // 处理 tsconfig paths
  alias: {
    '@': './src',
  },
})

// 复制火山云TLS所需的proto文件
try {
  // 确保输出目录存在
  mkdirSync('dist', { recursive: true })

  // 从node_modules复制tls.proto到dist目录
  const protoSrcPath = join(
    '..',
    '..',
    'packages',
    'huoshan',
    'node_modules',
    '@volcengine',
    'openapi',
    'lib',
    'services',
    'tls',
    'tls.proto',
  )
  const protoDestPath = join('dist', 'tls.proto')

  copyFileSync(protoSrcPath, protoDestPath)
  console.log('✅ 已复制 tls.proto 文件到构建目录')
} catch (error) {
  console.warn('⚠️ 复制 tls.proto 文件失败:', error.message)
  console.warn('如果不使用火山云TLS日志服务，可以忽略此警告')
}

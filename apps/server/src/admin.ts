import './env'
import { createApp } from './config/app'
import { registerPlugins } from './plugins/admin'
import { setupErrorHandler } from './handlers/error'
import { setupNotFoundHandler } from './handlers/not-found'
import { startServer } from './server'

async function main() {
  // 创建应用实例
  const app = createApp()

  // 注册所有插件
  await registerPlugins(app)

  // 设置处理器
  setupNotFoundHandler(app)
  setupErrorHandler(app)

  // 启动服务器
  await startServer(app, true)
}

main().catch((err) => {
  console.error('❌ 服务器启动失败:', err)
  process.exit(1)
})

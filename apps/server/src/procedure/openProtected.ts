import { db } from '@coozf/db'
import { publicApiProcedure, t } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { tokenService } from '@/lib/services/session'
import { applicationCache, accountCache, type CachedAccountInfo } from '@/lib/services/cache'
import { FastifyRequest } from 'fastify'
import { CookieService } from '@/lib/services/cookie'

// 验证token是否有效
export async function verifyToken(authorization?: string) {
  const token = authorization?.replace('Bearer ', '')
  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token 无效或已过期',
    })
  }

  const appId = await tokenService.verifyToken(token)
  if (!appId) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token 无效或已过期',
    })
  }

  const application = await applicationCache.getOrSetDefault(appId)

  if (!application) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '应用不存在',
    })
  }

  const { user, ...app } = application

  return {
    user,
    application: app,
  }
}

// 校验是否有secretKey
export function verifySecretKey(req: FastifyRequest) {
  const secretKey = req.headers['x-secret-key'] as string
  if (!secretKey) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: '请提供x-secret-key',
    })
  }
  return secretKey
}

// 校验是否有平台id
export async function verifyPlatformUserId(req: FastifyRequest, applicationId: string): Promise<CachedAccountInfo[]> {
  const secretKey = verifySecretKey(req)
  const id = req.headers['x-account-id'] as string
  if (!id) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: '请提供开放平台账号id:x-account-id',
    })
  }

  const ids = id.split(',')

  // 1. 批量从缓存获取账号信息
  const cachedAccounts = await accountCache.getMultiple(ids)

  // 2. 找出缓存未命中的账号ID
  const missedAccountIds = ids.filter((accountId) => cachedAccounts.get(accountId) === null)

  let newAccountInfos: CachedAccountInfo[] = []

  // 3. 处理缓存未命中的账号
  if (missedAccountIds.length > 0) {
    // 从数据库查询缓存未命中的账号
    const authAccounts = await db.authAccount.findMany({
      where: {
        id: {
          in: missedAccountIds,
        },
        applicationId,
        isDeleted: false,
      },
    })

    // 检查是否所有账号都存在
    if (authAccounts.length !== missedAccountIds.length) {
      const notFoundIds = missedAccountIds.filter((id) => !authAccounts.find((account) => account.id === id))
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '账号不存在,请先备案 id: ' + notFoundIds.join(','),
      })
    }

    // 4. 对新查询的账号进行解密处理
    newAccountInfos = await Promise.all(
      authAccounts.map(async (account) => {
        const cookie = await CookieService.decryptSafe(account.platformCookieHash, secretKey)

        if (!cookie.success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '数据解析失败',
          })
        }

        return {
          id: account.id,
          platformCode: account.platformCode,
          platformUserId: account.platformUserId,
          platformUserName: account.platformUserName,
          platformCookie: cookie.data!,
        }
      }),
    )

    // 5. 将新处理的账号信息存入缓存
    await accountCache.setMultiple(newAccountInfos)
  }

  // 6. 组合缓存命中和新处理的账号信息
  const allAccountInfos: CachedAccountInfo[] = []

  for (const accountId of ids) {
    const cached = cachedAccounts.get(accountId)
    if (cached) {
      allAccountInfos.push(cached)
    } else {
      const newInfo = newAccountInfos.find((info) => info.id === accountId)
      if (newInfo) {
        allAccountInfos.push(newInfo)
      }
    }
  }

  return allAccountInfos
}

export const openAPIMiddleware = t.middleware(async ({ ctx, next }) => {
  const userAppData = await verifyToken(ctx.req.headers.authorization)

  return next({
    ctx: {
      ...ctx,
      user: userAppData.user,
      application: userAppData.application,
    },
  })
})

export const openAPIProcedure = publicApiProcedure.use(openAPIMiddleware)

import { TRPCError } from '@trpc/server'
import z from 'zod'
import { adminProtectedProcedure, protectedProcedure } from '.'

export const applicationProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().cuid(),
    }),
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input

    const existingApp = await ctx.db.application.findUnique({
      where: {
        id: applicationId,
        userId: ctx.user.id,
      },
    })

    if (!existingApp) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        application: existingApp,
      },
    })
  })

export const applicationWithQuotaProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().cuid(),
    }),
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input

    // 验证权限并获取应用数据
    const application = await ctx.db.application.findUnique({
      where: {
        id: applicationId,
        userId: ctx.user.id,
      },
      include: {
        authAccounts: {
          select: {
            id: true,
          },
        },
      },
    })

    if (!application) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    const applicationWithQuota = {
      ...application,
      accountQuota: application.accountQuota || 0,
      authAccountsCount: application.authAccounts.length,
      trafficQuotaGB: application.trafficQuotaGB.toNumber(),
      trafficUsedGB: application.trafficUsedGB.toNumber(),
      accountQuotaExpireDate: application.accountQuotaExpireDate,
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        applicationWithQuota,
      },
    })
  })

// admin
export const adminApplicationProcedure = adminProtectedProcedure
  .input(
    z.object({
      applicationId: z.string().cuid(),
    }),
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input

    // 验证权限并获取应用数据
    const application = await ctx.db.application.findUnique({
      where: {
        id: applicationId,
      },
    })

    if (!application) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        application,
      },
    })
  })

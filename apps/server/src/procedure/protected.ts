import { t } from '@/trpc'
import { getAdminUser, getUser } from '@coozf/auth'
import { TRPCError } from '@trpc/server'

export const authMiddleware = t.middleware(async ({ ctx, next }) => {
  try {
    const user = await getUser(ctx.req)
    if (!user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Token无效或已过期',
      })
    }
    return next({
      ctx: {
        ...ctx,
        user,
      },
    })
  } catch (error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
})

export const protectedProcedure = t.procedure.use(authMiddleware)

export const adminAuthMiddleware = t.middleware(async ({ ctx, next }) => {
  const user = await getAdminUser(ctx.req)
  if (!user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token无效或已过期',
    })
  }
  return next({
    ctx: {
      ...ctx,
      user,
    },
  })
})

export const adminProtectedProcedure = t.procedure.use(adminAuthMiddleware)

import './env'
import { createApp } from './config/app'
import { registerPlugins } from './plugins'
import { setupErrorHandler } from './handlers/error'
import { setupNotFoundHandler } from './handlers/not-found'
import { startServer } from './server'
import * as cron from 'node-cron'
import { PublishService } from './lib/business/publish'
import { logger } from './lib/services/logger'
import { tokenRefreshScheduler } from './lib/business/token-refresh-scheduler'

async function main() {
  // 创建应用实例
  const app = createApp()
  // 设置处理器
  setupErrorHandler(app)
  setupNotFoundHandler(app)

  // 注册所有插件
  await registerPlugins(app)

  // 启动服务器
  await startServer(app)

  // 启动定时任务
  startScheduledTasks()
}

function startScheduledTasks() {

  // 超时自动失败任务 - 每小时执行一次
  cron.schedule(
    '0 * * * *',
    async () => {
      try {
        const count = await PublishService.autoFailTimeoutTasks(120) // 120分钟超时
        if (count > 0) {
          logger.info(`自动处理了 ${count} 个超时任务`, {
            type: 'cron',
            task: 'timeout-task-cleanup',
            count,
          })
        }
      } catch (error) {
        logger.error('超时任务处理失败', {
          type: 'cron',
          task: 'timeout-task-cleanup',
          error,
        })
      }
    },
    {
      name: 'timeout-task-cleanup',
      timezone: 'Asia/Shanghai',
    },
  )

  // 启动 Token 自动刷新任务 - 每30分钟执行一次
  cron.schedule(
    '*/30 * * * *',
    async () => {
      try {
        await tokenRefreshScheduler.refreshTokens()
        logger.info('过期刷新锁清理完成', {
          type: 'cron',
          task: 'token-refresh',
        })
      } catch (error) {
        logger.error('过期刷新锁清理失败', {
          type: 'cron',
          task: 'token-refresh',
          error,
        })
      }
    },
    {
      name: 'token-refresh',
      timezone: 'Asia/Shanghai',
    },
  )
  
  logger.info('定时任务已启动', {
    type: 'cron',
    tasks: ['timeout-task-cleanup', 'token-refresh'],
  })
  logger.info('超时任务清理: 每小时执行一次', {
    type: 'cron',
    schedule: '0 * * * *',
  })
  logger.info('Token 刷新: 每30分钟执行一次', {
    type: 'cron',
    schedule: '*/30 * * * *',
  })
}

main().catch((err) => {
  // 服务器启动失败时，使用简单的 console.error，因为日志服务可能还未初始化
  console.error('❌ 服务器启动失败:', err)
  process.exit(1)
})

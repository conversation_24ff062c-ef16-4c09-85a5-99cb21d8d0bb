import { cacheService } from './services/cache'
import { logger } from './services/logger'

// 基于Redis的设备管理器
class DeviceManager {
  private readonly DEVICE_PREFIX = 'device:'

  async connect(deviceId: string, socketId: string) {
    // 存储设备映射关系（有这个key就表示在线）
    await cacheService.set(
      `${this.DEVICE_PREFIX}${deviceId}`,
      {
        socketId,
        connectedAt: new Date(),
        lastSeen: new Date(),
      },
      60 * 60 * 24 * 30,
    )

    logger.info('设备连接', {
      type: 'device',
      event: 'connect',
      deviceId,
      socketId,
    })
  }

  async disconnect(deviceId: string) {
    await cacheService.del(`${this.DEVICE_PREFIX}${deviceId}`)
    logger.info('设备断开', {
      type: 'device',
      event: 'disconnect',
      deviceId,
    })
  }

  async getSocketId(deviceId: string): Promise<string | undefined> {
    const deviceInfo = await cacheService.get(`${this.DEVICE_PREFIX}${deviceId}`)
    return deviceInfo?.socketId
  }

  async isOnline(deviceId: string) {
    return await cacheService.exists(`${this.DEVICE_PREFIX}${deviceId}`)
  }

  async getAllDevices() {
    // 使用 SCAN 命令安全地查找所有设备键值对
    const deviceKeyValues = await cacheService.scanKeyValues(`${this.DEVICE_PREFIX}*`)

    const devices = []
    for (const [key, deviceInfo] of deviceKeyValues) {
      if (deviceInfo) {
        const deviceId = key.replace(this.DEVICE_PREFIX, '')
        devices.push({
          deviceId,
          ...deviceInfo,
        })
      }
    }

    return devices
  }

  async filterOnline(deviceIds: string[]) {
    // 使用 SCAN 命令查找所有设备键
    const keys = await cacheService.scanKeys(`${this.DEVICE_PREFIX}*`)
    const onlines = keys.map((key) => key.replace(this.DEVICE_PREFIX, ''))
      .filter((item) => deviceIds.includes(item))

    return onlines
  }

  async getStats() {
    // 使用 SCAN 命令统计设备数量
    const keys = await cacheService.scanKeys(`${this.DEVICE_PREFIX}*`)
    return { total: keys.length }
  }

  async clear() {
    // 使用 CacheService 的 clearPattern 方法，内部已经使用 SCAN
    const clearedCount = await cacheService.clearPattern(`${this.DEVICE_PREFIX}*`)

    logger.info('所有设备连接已清理', {
      type: 'device',
      event: 'clear_all',
      clearedCount,
    })
  }
}

export const deviceManager = new DeviceManager()

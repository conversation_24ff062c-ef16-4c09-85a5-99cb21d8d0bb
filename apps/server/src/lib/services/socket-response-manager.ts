import Redis from 'ioredis'
import type { FastifyInstance } from 'fastify'
import { ApiError, cacheService, logger } from '@/lib'
import * as cron from 'node-cron'

interface PendingRequest {
  resolve: (value: any) => void
  reject: (error: any) => void
  timeout: NodeJS.Timeout
  clientId: string
  startTime: number
}

class LocationRequestManager {
  private pendingRequests = new Map<string, PendingRequest>()
  private pubClient?: Redis
  private subClient?: Redis
  private instanceId: string
  private isInitialized = false

  constructor() {
    this.instanceId = this.generateInstanceId()
    logger.info('LocationRequestManager 实例ID', { instanceId: this.instanceId })
  }
  // 延迟初始化，在 Socket.IO 准备好后调用
  async initialize(fastify: FastifyInstance) {
    if (this.isInitialized) return

    try {
      // 复用现有的 Redis 实例
      const redisInstance = cacheService.getRedisInstance()
      this.pubClient = redisInstance
      this.subClient = redisInstance.duplicate()

      const locationResponseChannel = `location:response:${this.instanceId}`

      // 订阅本实例的响应通道
      await this.subClient.subscribe(locationResponseChannel)

      this.subClient.on('message', (channel, message) => {
        if (channel === locationResponseChannel) {
          try {
            const data = JSON.parse(message)
            this.handleLocalResponse(data.requestId, data.response)
          } catch (error) {
            logger.error('处理响应消息失败', { error, message })
          }
        }
      })
      this.isInitialized = true
      logger.info('LocationRequestManager 初始化成功', {
        instanceId: this.instanceId,
      })

      // 启动清理定时器
      this.startCleanupTimer()
    } catch (error) {
      logger.error('LocationRequestManager 初始化失败', { error })
      throw error
    }
  }

  // 创建请求并等待响应
  async waitForResponse(
    requestId: string,
    clientId: string,
    timeout = 30000,
  ): Promise<{
    success: boolean
    data?: any
    error?: string
  }> {
    return new Promise((resolve, reject) => {
      const timeoutHandle = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new ApiError(40008, `定位请求超时 (${timeout}ms)`))
      }, timeout)

      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout: timeoutHandle,
        clientId,
        startTime: Date.now(),
      })
    })
  }

  // 处理响应（从 Socket 调用）
  async handleResponse(requestId: string, response: any) {
    // 先检查是否是本实例的请求
    const pending = this.pendingRequests.get(requestId)
    if (pending) {
      // 是本实例的请求，直接处理
      this.handleLocalResponse(requestId, response)
    } else {
      // 可能是其他实例的请求，通过 Redis 转发
      const [instanceId] = requestId.split('-')
      if (instanceId && instanceId !== this.instanceId) {
        await this.forwardResponse(instanceId, requestId, response)
      }
    }
  }

  // 处理本地响应
  private handleLocalResponse(requestId: string, response: any) {
    const pending = this.pendingRequests.get(requestId)
    if (pending) {
      clearTimeout(pending.timeout)
      const duration = Date.now() - pending.startTime

      logger.info('处理定位响应', {
        type: 'location',
        event: 'location_response',
        requestId,
        clientId: pending.clientId,
        duration: `${duration}ms`,
      })

      pending.resolve(response)
      this.pendingRequests.delete(requestId)
    }
  }

  // 转发响应到其他实例
  private async forwardResponse(targetInstanceId: string, requestId: string, response: any) {
    if (!this.pubClient) {
      logger.error('Redis 客户端未初始化')
      return
    }

    try {
      await this.pubClient.publish(`location:response:${targetInstanceId}`, JSON.stringify({ requestId, response }))

      logger.info('转发响应到其他实例', {
        targetInstanceId,
        requestId,
      })
    } catch (error) {
      logger.error('转发响应失败', { error, targetInstanceId, requestId })
    }
  }

  // 生成包含实例ID的请求ID
  generateRequestId(clientId: string): string {
    return `${this.instanceId}-${clientId}-${Date.now()}-${Math.random().toString(36).substring(7)}`
  }

  private generateInstanceId(): string {
    return Math.random().toString(36).substring(2, 9)
  }

  // 清理超时的请求
  private startCleanupTimer() {
    cron.schedule(
      '0 * * * *',
      async () => {
        try {
          const now = Date.now()
          for (const [requestId, request] of this.pendingRequests) {
            if (now - request.startTime > 60000) {
              clearTimeout(request.timeout)
              this.pendingRequests.delete(requestId)
              logger.info('清理超时请求', { requestId })
            }
          }
        } catch (error) {
          logger.error('清理超时请求', {
            type: 'cron',
            task: 'location-request-cleanup',
            error,
          })
        }
      },
      {
        name: 'location-request-cleanup',
        timezone: 'Asia/Shanghai',
      },
    )
  }

  // 清理资源
  async cleanup() {
    // 清理所有待处理请求
    for (const [requestId, request] of this.pendingRequests) {
      clearTimeout(request.timeout)
      request.reject(new Error('Service shutting down'))
    }
    this.pendingRequests.clear()

    // 关闭 Redis 连接
    if (this.pubClient) await this.pubClient.disconnect()
    if (this.subClient) await this.subClient.disconnect()
  }

  // 获取统计信息
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      instanceId: this.instanceId,
      initialized: this.isInitialized,
    }
  }
}

// 创建全局实例
export const locationRequestManager = new LocationRequestManager()

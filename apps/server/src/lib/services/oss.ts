import { env } from '@/env'
import { storageService } from '@coozf/huoshan'

export class AdminOssService {
  private deskTopBucketName = env.OSS_DESKTOP_BUCKET

  async getListObjects(name: string): Promise<any> {
    try {
      const getListObjects = await storageService.getFileList(name, this.deskTopBucketName)
      return getListObjects
    } catch (error) {
      throw new Error(`获取文件列表失败: ${error}`)
    }
  }

  async getUploadSignatureUrl(key: string) {
    const signatureUrl = await storageService.getUploadUrl(key, undefined, this.deskTopBucketName)
    return {
      serviceUrl: signatureUrl,
      key: key,
      url: `https://yixiaoer-lite-desktop-download.tos-cn-shanghai.volces.com/${key}`,
    }
  }

  /**
   * 上传文件到oss
   * @param buffer
   * @param name
   * @returns
   */
  async uploadFile(buffer: Buffer, name: string) {
    try {
      await storageService.uploadFile(buffer, name)
      return name
    } catch (error) {
      throw new Error(`上传文件失败: ${error}`)
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await storageService.deleteFile(key)
    } catch (error) {
      throw new Error(`删除文件失败: ${error}`)
    }
  }
}

export const adminOssService = new AdminOssService()

import crypto from 'crypto'
import axios from 'axios'
import { nanoid } from 'nanoid'
import { env } from '../../env'
import { logger } from './logger'

// 支持的OAuth平台
export const SUPPORTED_PLATFORMS = ['xiaoh<PERSON><PERSON>', 'douyin', 'kuaishou', 'weibo'] as const
export type PlatformType = (typeof SUPPORTED_PLATFORMS)[number]

// 平台配置接口
interface PlatformConfig {
  clientId: string
  clientSecret: string
  authorizeUrl: string
  tokenUrl: string
  apiBase: string
  scopes: string
}

// OAuth响应类型
export interface OAuthTokenResponse {
  success: boolean
  data?: {
    user_id: string
    access_token: string
    refresh_token: string
    expires_in: number
    scope: string
    [key: string]: unknown
  }
  error?: string
  message?: string
}

// 用户信息响应类型
export interface UserInfoResponse {
  success: boolean
  data?: {
    user_id: string
    nickname?: string
    avatar?: string
    [key: string]: unknown
  }
  error?: string
  message?: string
}

/**
 * OAuth服务
 */
export class OAuthService {
  private static readonly PLATFORM_CONFIGS: Record<PlatformType, PlatformConfig> = {
    xiaohongshu: {
      clientId: env.XIAOHONGSHU_CLIENT_KEY,
      clientSecret: env.XIAOHONGSHU_CLIENT_SECRET,
      authorizeUrl: 'https://ad-market.xiaohongshu.com/auth',
      tokenUrl: 'https://adapi.xiaohongshu.com/api/open/oauth2/access_token',
      apiBase: 'https://adapi.xiaohongshu.com',
      scopes: '["third_im","three_im_leads_push"]',
    },
    douyin: {
      clientId: '', // 暂时留空，待配置
      clientSecret: '',
      authorizeUrl: 'https://open.douyin.com/platform/oauth/connect',
      tokenUrl: 'https://open.douyin.com/oauth/access_token',
      apiBase: 'https://open.douyin.com',
      scopes: '["user_info","video.list"]',
    },
    kuaishou: {
      clientId: '', // 暂时留空，待配置
      clientSecret: '',
      authorizeUrl: 'https://open.kuaishou.com/oauth2/authorize',
      tokenUrl: 'https://open.kuaishou.com/oauth2/access_token',
      apiBase: 'https://open.kuaishou.com',
      scopes: '["user_info","video.publish"]',
    },
    weibo: {
      clientId: '', // 暂时留空，待配置
      clientSecret: '',
      authorizeUrl: 'https://api.weibo.com/oauth2/authorize',
      tokenUrl: 'https://api.weibo.com/oauth2/access_token',
      apiBase: 'https://api.weibo.com',
      scopes: '["email","direct_messages_read"]',
    },
  }

  /**
   * 获取平台配置
   */
  static getPlatformConfig(platform: PlatformType): PlatformConfig {
    const config = this.PLATFORM_CONFIGS[platform]
    if (!config.clientId || !config.clientSecret) {
      throw new Error(`平台 ${platform} 配置不完整，请检查环境变量`)
    }
    return config
  }

  /**
   * 生成OAuth状态参数
   * 格式: appId:platform:timestamp:random
   */
  static generateOAuthState(appId: string, platform: PlatformType, customState?: string): string {
    const timestamp = Date.now()
    const random = nanoid(8)
    const baseState = `${appId}:${platform}:${timestamp}:${random}`

    if (customState) {
      return `${baseState}:${customState}`
    }

    return baseState
  }

  /**
   * 解析OAuth状态参数
   */
  static parseOAuthState(state: string): {
    appId: string
    platform: PlatformType
    timestamp: number
    random: string
    customState?: string
  } | null {
    const parts = state.split(':')
    if (parts.length < 4) {
      return null
    }

    const [appId, platform, timestampStr, random, ...customStateParts] = parts
    const timestamp = parseInt(timestampStr || '', 10)

    if (
      !appId ||
      !platform ||
      !timestampStr ||
      !random ||
      !SUPPORTED_PLATFORMS.includes(platform as PlatformType) ||
      isNaN(timestamp)
    ) {
      return null
    }

    return {
      appId,
      platform: platform as PlatformType,
      timestamp,
      random,
      customState: customStateParts.length > 0 ? customStateParts.join(':') : undefined,
    }
  }

  /**
   * 生成平台授权URL
   */
  static generateAuthorizeUrl(
    platform: PlatformType,
    redirectUri: string,
    state: string,
    scope?: string
  ): string {
    const config = this.getPlatformConfig(platform)
    const finalScope = scope || config.scopes || 'basic_info'

    const params = new URLSearchParams()

    // 各平台参数名称可能不同，需要适配
    switch (platform) {
      case 'xiaohongshu':
        params.set('appId', config.clientId)
        params.set('redirectUri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        break

      case 'douyin':
        params.set('client_key', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break

      case 'kuaishou':
        params.set('app_id', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break

      case 'weibo':
        params.set('client_id', config.clientId)
        params.set('redirect_uri', redirectUri)
        params.set('scope', finalScope)
        params.set('state', state)
        params.set('response_type', 'code')
        break
    }

    const url = new URL(config.authorizeUrl)
    url.search = params.toString()

    return url.toString()
  }

  /**
   * 通过授权码获取访问令牌（仅实现小红书，其他平台待后续扩展）
   */
  static async getAccessToken(platform: PlatformType, authCode: string): Promise<OAuthTokenResponse> {
    if (platform !== 'xiaohongshu') {
      return {
        success: false,
        error: `暂不支持 ${platform} 平台`,
      }
    }

    const config = this.getPlatformConfig(platform)

    try {
      const requestData = {
        app_id: config.clientId,
        secret: config.clientSecret,
        auth_code: authCode,
      }

      const response = await axios.post(config.tokenUrl, requestData)

      const advertisers = response.data.data.approval_advertisers
      const userInfo = advertisers.length > 0 ? advertisers[0] : null

      if (response.data.success) {
        return {
          success: true,
          data: {
            user_id: response.data.data.user_id,
            access_token: response.data.data.access_token,
            refresh_token: response.data.data.refresh_token,
            expires_in: response.data.data.access_token_expires_in,
            scope: response.data.data.scope,
            name: userInfo.nickname,
          },
        }
      } else {
        return {
          success: false,
          error: response.data.msg || '获取访问令牌失败',
        }
      }
    } catch (error) {
      logger.error('OAuth获取访问令牌失败', {
        type: 'oauth',
        platform,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
      })
      return {
        success: false,
        error: `获取访问令牌失败: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }
}
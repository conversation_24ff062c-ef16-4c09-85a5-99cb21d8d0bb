import { Worker, Job } from 'bullmq'
import { logger } from '@/lib'
import type { UpdateMessage } from './message-publisher'
import type { FastifyInstance } from 'fastify'
import { config } from '@/env'

/**
 * BullMQ 消息处理器
 * 用于处理版本更新任务并通过 Socket.IO 推送给客户端
 */
export class MessageSubscriber {
  private worker: Worker<UpdateMessage> | null = null
  private app: FastifyInstance | null = null
  private isStarted = false

  /**
   * 设置 Fastify 应用实例
   */
  setApp(app: FastifyInstance): void {
    this.app = app
  }

  /**
   * 启动消息处理器
   */
  async start(): Promise<void> {
    if (this.isStarted) {
      logger.warn('消息处理器已经启动', {
        type: 'message_subscriber',
        event: 'already_started',
      })
      return
    }

    try {
      // 创建 Worker 来处理队列中的任务
      this.worker = new Worker<UpdateMessage>(
        config.UPDATE_CHANNEL, // 队列名称
        async (job: Job<UpdateMessage>) => {
          // 处理版本更新任务
          await this.processUpdateJob(job)
        },
        {
          connection: {
            host: config.REDIS_HOST,
            port: config.REDIS_PORT,
            password: config.REDIS_PASSWORD,
            db: config.REDIS_DB,
          },
          concurrency: 5, // 同时处理的任务数
          autorun: true, // 自动开始处理
        },
      )

      // 设置事件监听
      this.setupWorkerEvents()

      this.isStarted = true

      logger.info('消息处理器启动成功', {
        type: 'message_subscriber',
        event: 'worker_started',
        workerId: this.worker.id,
      })

      // 清理资源的处理
      process.on('SIGTERM', () => this.cleanup())
      process.on('SIGINT', () => this.cleanup())
    } catch (error) {
      logger.error('消息处理器启动失败', {
        type: 'message_subscriber',
        event: 'start_failed',
        error: error instanceof Error ? error.message : error,
      })
      throw error
    }
  }

  /**
   * 设置 Worker 事件监听
   */
  private setupWorkerEvents(): void {
    if (!this.worker) return

    // 任务完成事件
    this.worker.on('completed', (job: Job<UpdateMessage>) => {
      logger.info('版本更新任务处理完成', {
        type: 'message_subscriber',
        event: 'job_completed',
        jobId: job.id,
        versionType: job.data.type,
        version: job.data.version,
      })
    })

    // 任务失败事件
    this.worker.on('failed', (job: Job<UpdateMessage> | undefined, error: Error) => {
      logger.error('版本更新任务处理失败', {
        type: 'message_subscriber',
        event: 'job_failed',
        jobId: job?.id,
        versionType: job?.data.type,
        version: job?.data.version,
        error: error.message,
      })
    })

    // Worker 错误事件
    this.worker.on('error', (error: Error) => {
      logger.error('Worker 错误', {
        type: 'message_subscriber',
        event: 'worker_error',
        error: error.message,
      })
    })

    // 任务停滞事件
    this.worker.on('stalled', (jobId: string) => {
      logger.warn('任务停滞', {
        type: 'message_subscriber',
        event: 'job_stalled',
        jobId,
      })
    })
  }

  /**
   * 处理版本更新任务
   */
  private async processUpdateJob(job: Job<UpdateMessage>): Promise<unknown> {
    const message = job.data

    try {
      logger.info('开始处理版本更新任务', {
        type: 'message_subscriber',
        event: 'processing_job',
        channel: config.UPDATE_CHANNEL,
        jobId: job.id,
        versionType: message.type,
        version: message.version,
        forceUpdate: message.forceUpdate,
      })

      // 更新任务进度
      await job.updateProgress(50)

      // 通过 Socket.IO 推送给客户端
      await this.broadcastUpdateToClients(message)

      // 更新任务进度
      await job.updateProgress(100)

      // 可以返回处理结果，会存储在 job.returnvalue 中
      return {
        success: true,
        broadcastedAt: new Date().toISOString(),
        clientsCount: this.app?.io?.engine.clientsCount || 0,
      }
    } catch (error) {
      logger.error('处理版本更新任务失败', {
        type: 'message_subscriber',
        event: 'process_job_failed',
        jobId: job.id,
        message,
        error: error instanceof Error ? error.message : error,
      })
      throw error // 重新抛出错误，让 BullMQ 处理重试
    }
  }

  /**
   * 向所有在线客户端广播更新消息
   */
  private async broadcastUpdateToClients(message: UpdateMessage): Promise<void> {
    if (!this.app?.io) {
      throw new Error('Socket.IO 实例不可用')
    }

    try {
      // 根据版本类型发送对应的 socket 事件
      let eventName: string

      switch (message.type) {
        case 'CRAWLER':
          eventName = 'update:reptile'
          break
        case 'RPA':
          eventName = 'update:rpa'
          break
        case 'DESKTOP':
          eventName = 'update'
          break
        case 'BROWSER_PLUGIN':
          eventName = 'update:browser'
          break
        default:
          throw new Error(`未知的版本类型: ${message.type}`)
      }

      // 获取当前连接的客户端数量
      const sockets = await this.app.io.fetchSockets()
      const connectedClients = sockets.length

      // 添加唯一消息 ID
      const messageId = crypto.randomUUID()

      // 向所有连接的客户端广播消息
      this.app.io.volatile.emit(eventName, {
        messageId, // 添加消息 ID
        version: message.version,
        forceUpdate: message.forceUpdate,
        description: message.description,
        downloadUrl: message.downloadUrl,
        timestamp: message.timestamp,
      })

      logger.info('更新消息已广播给客户端', {
        type: 'message_subscriber',
        event: 'update_broadcasted',
        eventName,
        versionType: message.type,
        version: message.version,
        connectedClients,
        messageId,
      })
    } catch (error) {
      logger.error('广播更新消息失败', {
        type: 'message_subscriber',
        event: 'broadcast_failed',
        message,
        error: error instanceof Error ? error.message : error,
      })
      throw error
    }
  }

  /**
   * 暂停处理
   */
  async pause(): Promise<void> {
    if (this.worker) {
      await this.worker.pause()
      logger.info('消息处理器已暂停', {
        type: 'message_subscriber',
        event: 'worker_paused',
      })
    }
  }

  /**
   * 恢复处理
   */
  async resume(): Promise<void> {
    if (this.worker) {
      await this.worker.resume()
      logger.info('消息处理器已恢复', {
        type: 'message_subscriber',
        event: 'worker_resumed',
      })
    }
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.worker) {
        await this.worker.close()
        this.worker = null
      }
      this.isStarted = false

      logger.info('消息处理器已清理', {
        type: 'message_subscriber',
        event: 'cleanup_completed',
      })
    } catch (error) {
      logger.error('清理消息处理器失败', {
        type: 'message_subscriber',
        event: 'cleanup_failed',
        error: error instanceof Error ? error.message : error,
      })
    }
  }

  /**
   * 获取处理器状态
   */
  getStatus(): {
    isStarted: boolean
    workerId: string | null
    isPaused: boolean
  } {
    return {
      isStarted: this.isStarted,
      workerId: this.worker?.id || null,
      isPaused: this.worker?.isPaused() || false,
    }
  }
}

// 导出单例实例
export const messageSubscriber = new MessageSubscriber()

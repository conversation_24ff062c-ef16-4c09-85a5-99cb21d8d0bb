import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525'
import * as $OpenApi from '@alicloud/openapi-client'
import * as Util from '@alicloud/tea-util'
import { config } from '../../env'
import { TRPCError } from '@trpc/server'
import { logger } from './logger'

export interface SmsConfig {
  accessKeyId: string
  accessKeySecret: string
  endpoint: string
  signName: string
  templateCode: string
}

/**
 * 短信服务
 */
export class SmsService {
  private config: SmsConfig

  constructor(smsConfig?: SmsConfig) {
    this.config = smsConfig || {
      accessKeyId: config.smsAccessKeyId,
      accessKeySecret: config.smsAccessKeySecret,
      endpoint: config.smsEndpoint,
      signName: config.smsSignName,
      templateCode: config.smsTemplateCode,
    }
  }

  /**
   * 发送短信验证码
   */
  async sendVerificationCode(phone: string, code: string): Promise<{ success: boolean; message?: string }> {
    try {
      const openApiConfig = new $OpenApi.Config({
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
      })
      openApiConfig.endpoint = this.config.endpoint

      const dysmsapiClient = new Dysmsapi20170525(openApiConfig)

      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        phoneNumbers: phone,
        signName: this.config.signName,
        templateCode: this.config.templateCode,
        templateParam: `{"code":"${code}"}`,
      })

      const runtime = new Util.RuntimeOptions({})
      const response = await dysmsapiClient.sendSmsWithOptions(sendSmsRequest, runtime)

      if (response.body?.code === 'OK') {
        return { success: true }
      } else {
        return {
          success: false,
          message: response.body?.message || '短信发送失败',
        }
      }
    } catch (error) {
      logger.error('短信发送失败', {
        type: 'sms',
        phone,
        templateCode: this.config.templateCode,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
      })
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '短信发送失败',
      })
    }
  }

  /**
   * 批量发送短信
   */
  async sendBatchSMS(phones: string[], message: string): Promise<{ success: boolean; results: any[] }> {
    // 批量发送逻辑
    const results = []
    for (const phone of phones) {
      try {
        const result = await this.sendVerificationCode(phone, message)
        results.push({ phone, ...result })
      } catch (error) {
        results.push({ phone, success: false, error: error })
      }
    }
    return {
      success: results.every((r) => r.success),
      results,
    }
  }
}

// 创建默认实例
export const smsService = new SmsService()

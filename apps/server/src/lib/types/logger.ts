import type { FastifyRequest } from 'fastify'

export type LogLevel = 'info' | 'error' | 'warn' | 'debug'

export interface LogContext {
  requestId?: string
  userId?: string
  method?: string
  url?: string
  statusCode?: number
  userAgent?: string
  ip?: string
  [key: string]: any
}

export interface LoggerProvider {
  info(message: string, context?: LogContext): Promise<void>
  error(message: string, context?: LogContext): Promise<void>
  warn(message: string, context?: LogContext): Promise<void>
  debug(message: string, context?: LogContext): Promise<void>
}

export interface LoggerConfig {
  enableTls?: boolean
  enableConsole?: boolean
  level?: LogLevel
}
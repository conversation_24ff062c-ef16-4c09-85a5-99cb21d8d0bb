import type { Application } from '@prisma/client'

// 基于Prisma类型的配额信息
export type ApplicationQuotaInfo = Pick<Application, 
  'accountQuota' | 'trafficQuotaGB' | 'trafficUsedGB' | 'accountQuotaExpireDate'
>

// 应用配额概览
export interface QuotaOverview {
  account: {
    quota: number
    used: number
    available: number
    expireDate: Date | null
    expired: boolean
  }
  traffic: {
    quotaGB: number
    usedGB: number
    availableGB: number
    usagePercent: number
  }
}

// 配额检查结果
export interface QuotaCheckResult {
  sufficient: boolean
  reason?: string
}

// 流量消费参数
export interface ConsumeTrafficParams {
  applicationId: string
  trafficGB: number
  endpoint?: string
  description?: string
}

// 账号配额检查参数
export interface CheckAccountQuotaParams {
  applicationId: string
  requiredAccounts: number
}

// 流量配额检查参数
export interface CheckTrafficQuotaParams {
  applicationId: string
  requiredTrafficGB: number
}
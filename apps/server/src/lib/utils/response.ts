/**
 * API 响应格式化工具
 */

// 定义标准 API 响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T | null
  [key: string]: any // 允许额外字段
}

// 定义业务错误类
export class ApiError extends Error {
  public code: number

  constructor(code: number, message: string) {
    super(message)
    this.code = code
    this.name = 'ApiError'
  }
}

// 响应封装器
export class ResponseWrapper {
  /**
   * 创建一个成功的 API 响应
   */
  public static success<T>(data: T, message = '操作成功'): ApiResponse<T> {
    return {
      code: 0,
      message,
      data,
    }
  }

  /**
   * 创建一个失败的 API 响应
   */
  public static error(code: number, message: string, extra?: Record<string, any>): ApiResponse<null> {
    const response: ApiResponse<null> = {
      code,
      message,
      data: null,
    }
    
    // 如果有额外数据，合并到响应中
    if (extra) {
      Object.assign(response, extra)
    }
    
    return response
  }
}

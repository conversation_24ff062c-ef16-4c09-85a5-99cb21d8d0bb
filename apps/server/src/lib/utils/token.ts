import jwt from 'jsonwebtoken'

export interface AccessTokenPayload {
  // 核心信息
  appId: string // 应用ID
  exp: number // 过期时间
  iat: number // 签发时间
  jti: string // JWT ID（唯一标识）
}

/**
 * 生成 JWT Token
 */
export function generateJWT(payload: AccessTokenPayload, secret: string): string {
  return jwt.sign(payload, secret)
}

/**
 * 验证 JWT Token
 */
export function verifyJWT(token: string, secret: string): AccessTokenPayload | null {
  try {
    return jwt.verify(token, secret) as AccessTokenPayload
  } catch {
    return null
  }
}

/**
 * 生成Token载荷
 */
export function createTokenPayload(appId: string, expiresInSeconds: number = 30 * 24 * 60 * 60): AccessTokenPayload {
  const now = Math.floor(Date.now() / 1000)
  return {
    appId,
    exp: now + expiresInSeconds,
    iat: now,
    jti: crypto.randomUUID(),
  }
}
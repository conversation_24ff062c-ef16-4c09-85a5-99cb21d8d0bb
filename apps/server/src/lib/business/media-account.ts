import { db } from '@coozf/db'
import { AccountListQuery, PlatformCode } from '@coozf/zod'
import { Prisma } from '@prisma/client'
import { paginate } from '../utils'

export class MediaAccountServies {
  static async getMediaAccountOrders(params: AccountListQuery & { applicationId?: string }) {
    const { page = 1, pageSize = 10, platformCode, search, applicationId, startDate, endDate } = params
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.AuthAccountWhereInput = {
      isDeleted: false,
    }
    if (applicationId) {
      where.applicationId = applicationId
    }

    if (platformCode) {
      where.platformCode = platformCode
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    if (search) {
      where.OR = [
        {
          platformUserId: {
            contains: search,
          },
        },
        {
          platformUserName: {
            contains: search,
          },
        },
      ]
    }

    return paginate(
      {
        page,
        pageSize,
      },
      {
        getTotal: () => db.authAccount.count({ where }),
        getItems: (skip, take) =>
          db.authAccount.findMany({
            where,
            skip,
            take,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              applicationId: true,
              platformCode: true,
              platformUserId: true,
              platformUserName: true,
              platformAvatar: true,
              createdAt: true,
              updatedAt: true,
            }
          }),
        transform: (account) => ({
          id: account.id,
          applicationId: account.applicationId,
          platformCode: account.platformCode as PlatformCode,
          platformUserId: account.platformUserId,
          platformUserName: account.platformUserName,
          platformAvatar: account.platformAvatar,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt,
        }),
      },
    )
  }

  static async getFindFirst(params: Prisma.AuthAccountWhereInput) {
    return db.authAccount.findFirst({
      where: {
        ...params,
        isDeleted: false,
      },
    })
  }
}

import { db } from '@coozf/db'
import type { PlatformType } from '../services/oauth'
import { logger } from '../services/logger'
import { CacheService } from '../services/cache'
import { env } from '../../env'
import axios from 'axios'

/**
 * Token 数据结构接口
 */
interface TokenData {
  access_token: string
  refresh_token: string
  expires_in: number
  scope?: string
  [key: string]: any
}

/**
 * Token 刷新锁管理器
 * 防止同一账号的 token 被并发刷新
 */
class RefreshLockManager {
  private static instance: RefreshLockManager
  private cacheService: CacheService
  private readonly LOCK_PREFIX = 'token_refresh_lock:'
  private readonly LOCK_TTL = 300 // 5分钟锁定时间

  private constructor() {
    this.cacheService = new CacheService()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): RefreshLockManager {
    if (!this.instance) {
      this.instance = new RefreshLockManager()
    }
    return this.instance
  }

  /**
   * 尝试获取账号刷新锁
   * @param accountId 账号ID
   * @returns 是否成功获取锁
   */
  async acquireLock(accountId: string): Promise<boolean> {
    const lockKey = `${this.LOCK_PREFIX}${accountId}`
    
    try {
      // 使用 Redis SET NX EX 命令实现分布式锁
      const result = await this.cacheService.setNX(lockKey, Date.now().toString(), this.LOCK_TTL)
      
      if (result) {
        logger.debug(`成功获取账号 ${accountId} 的刷新锁`, {
          type: 'refresh-lock',
          accountId,
          action: 'acquire'
        })
      } else {
        logger.debug(`账号 ${accountId} 的刷新锁已被占用`, {
          type: 'refresh-lock',
          accountId,
          action: 'acquire-failed'
        })
      }
      
      return result
    } catch (error) {
      logger.error(`获取账号 ${accountId} 刷新锁失败`, {
        type: 'refresh-lock',
        accountId,
        action: 'acquire-error',
        error
      })
      return false
    }
  }

  /**
   * 释放账号刷新锁
   * @param accountId 账号ID
   */
  async releaseLock(accountId: string): Promise<void> {
    const lockKey = `${this.LOCK_PREFIX}${accountId}`
    
    try {
      await this.cacheService.del(lockKey)
      
      logger.debug(`释放账号 ${accountId} 的刷新锁`, {
        type: 'refresh-lock',
        accountId,
        action: 'release'
      })
    } catch (error) {
      logger.error(`释放账号 ${accountId} 刷新锁失败`, {
        type: 'refresh-lock',
        accountId,
        action: 'release-error',
        error
      })
    }
  }

  /**
   * 批量获取锁
   * @param accountIds 账号ID列表
   * @returns 成功获取锁的账号ID列表
   */
  async batchAcquireLocks(accountIds: string[]): Promise<string[]> {
    const successfulLocks: string[] = []
    
    for (const accountId of accountIds) {
      const acquired = await this.acquireLock(accountId)
      if (acquired) {
        successfulLocks.push(accountId)
      }
    }
    
    logger.info(`批量获取刷新锁完成`, {
      type: 'refresh-lock',
      action: 'batch-acquire',
      requested: accountIds.length,
      acquired: successfulLocks.length,
      failed: accountIds.length - successfulLocks.length
    })
    
    return successfulLocks
  }

  /**
   * 批量释放锁
   * @param accountIds 账号ID列表
   */
  async batchReleaseLocks(accountIds: string[]): Promise<void> {
    const releasePromises = accountIds.map(accountId => this.releaseLock(accountId))
    
    try {
      await Promise.all(releasePromises)
      
      logger.info(`批量释放刷新锁完成`, {
        type: 'refresh-lock',
        action: 'batch-release',
        count: accountIds.length
      })
    } catch (error) {
      logger.error(`批量释放刷新锁失败`, {
        type: 'refresh-lock',
        action: 'batch-release-error',
        count: accountIds.length,
        error
      })
    }
  }

  /**
   * 使用锁执行操作（推荐的使用方式）
   * @param accountId 账号ID
   * @param operation 要执行的操作
   * @returns 操作结果
   */
  async withLock<T>(
    accountId: string,
    operation: () => Promise<T>
  ): Promise<{ success: boolean; result?: T; error?: string }> {
    const acquired = await this.acquireLock(accountId)
    
    if (!acquired) {
      return {
        success: false,
        error: '无法获取锁，账号可能正在被其他进程刷新'
      }
    }

    try {
      const result = await operation()
      return {
        success: true,
        result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '操作执行失败'
      }
    } finally {
      await this.releaseLock(accountId)
    }
  }
}

/**
 * Token 刷新服务
 * 负责自动刷新媒体账号的 access_token
 */
class TokenRefreshService {
  private static readonly REFRESH_THRESHOLD_MINUTES = 30 // 30分钟内过期的 token 需要刷新
  private static readonly CONCURRENT_REFRESH_LIMIT = 5 // 并发刷新限制
  private static lockManager = RefreshLockManager.getInstance()

  /**
   * 获取需要刷新的账号列表
   * @returns 需要刷新的账号列表
   */
  static async getAccountsNeedingRefresh(): Promise<any[]> {
    const thresholdTime = new Date(Date.now() + this.REFRESH_THRESHOLD_MINUTES * 60 * 1000)

    try {
      const accounts = await db.authAccount.findMany({
        where: {
          isDeleted: false,
          authType: 'TOKEN', // 只处理 TOKEN 类型的账号
          platformTokenExpiresAt: {
            lte: thresholdTime // 即将过期或已过期的 token
          }
        },
        include: {
          application: {
            select: {
              id: true,
              name: true,
              appId: true,
              secret: true
            }
          }
        },
        orderBy: {
          platformTokenExpiresAt: 'asc' // 优先处理最快过期的
        }
      })

      logger.info(`发现 ${accounts.length} 个账号需要刷新 token`, {
        type: 'token-refresh',
        count: accounts.length,
        threshold: this.REFRESH_THRESHOLD_MINUTES
      })

      return accounts
    } catch (error) {
      logger.error('获取需要刷新的账号列表失败', {
        type: 'token-refresh',
        error
      })
      throw error
    }
  }

  /**
   * 刷新单个账号的 token
   * @param accountId 账号ID
   * @returns 刷新结果
   */
  static async refreshAccountToken(accountId: string): Promise<{
    success: boolean
    message: string
    newTokenExpiresAt?: Date
  }> {
    // 使用锁机制防止并发刷新
    return await this.lockManager.withLock(accountId, async () => {
      // 使用事务确保数据一致性
      const result = await db.$transaction(async (tx) => {
        // 获取账号信息
        const account = await tx.authAccount.findUnique({
          where: { id: accountId },
          include: {
            application: true
          }
        })

        if (!account) {
          throw new Error('账号不存在')
        }

        if (!account.platformCookieHash) {
          throw new Error('缺少 token 数据')
        }

        let tokenData: TokenData
        try {
          tokenData = JSON.parse(account.platformCookieHash!)
        } catch {
          throw new Error('Token 数据格式错误')
        }

        if (!tokenData.refresh_token) {
          throw new Error('缺少刷新令牌')
        }

        // 调用平台 API 刷新 token
        const refreshResult = await this.callPlatformRefreshAPI(
          account.platformCode as PlatformType,
          tokenData.refresh_token
        )

        if (!refreshResult.success) {
          throw new Error(refreshResult.error || '刷新失败')
        }

        // 刷新成功，更新 token 信息
        const newTokenExpiresAt = new Date(Date.now() + refreshResult.data!.expires_in * 1000)

        // 构建新的 token 数据
        const newTokenData: TokenData = {
          ...tokenData,
          access_token: refreshResult.data!.access_token,
          refresh_token: refreshResult.data!.refresh_token || tokenData.refresh_token,
          expires_in: refreshResult.data!.expires_in,
          scope: refreshResult.data!.scope || tokenData.scope
        }

        // 保存新的 token 数据
        const tokenJsonStr = JSON.stringify(newTokenData)

        await tx.authAccount.update({
          where: { id: accountId },
          data: {
            platformCookieHash: tokenJsonStr,
            platformTokenExpiresAt: newTokenExpiresAt,
            updatedAt: new Date()
          }
        })

        return {
          success: true,
          message: '刷新成功',
          newTokenExpiresAt
        }
      })

      logger.info(`账号 ${accountId} token 刷新成功`, {
        type: 'token-refresh',
        accountId,
        newExpiresAt: result.newTokenExpiresAt
      })

      return result
    })
    .then(lockResult => {
      if (!lockResult.success) {
        logger.warn(`账号 ${accountId} token 刷新被跳过`, {
          type: 'token-refresh',
          accountId,
          reason: lockResult.error
        })
        return {
          success: false,
          message: lockResult.error || '获取锁失败'
        }
      }
      return lockResult.result!
    })
    .catch(error => {
      const errorMessage = error instanceof Error ? error.message : '未知错误'

      logger.error(`账号 ${accountId} token 刷新失败`, {
        type: 'token-refresh',
        accountId,
        error: errorMessage
      })

      return {
        success: false,
        message: errorMessage
      }
    })
  }

  /**
   * 调用平台 API 刷新 token
   * @param platform 平台类型
   * @param refreshToken 刷新令牌
   * @returns 刷新结果
   */
  private static async callPlatformRefreshAPI(
    platform: PlatformType,
    refreshToken: string
  ): Promise<{
    success: boolean
    data?: {
      access_token: string
      refresh_token?: string
      expires_in: number
      scope?: string
      [key: string]: any
    }
    error?: string
  }> {
    try {
      // 这里调用各平台的刷新 API
      switch (platform) {
        case 'xiaohongshu':
          return await this.refreshXiaohongshuToken(refreshToken)

        default:
          return {
            success: false,
            error: `暂不支持 ${platform} 平台的 token 刷新`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '刷新 API 调用失败'
      }
    }
  }

  /**
   * 刷新小红书 token
   * 参考 qdy-service 中的实现
   */
  private static async refreshXiaohongshuToken(refreshToken: string) {
    try {
      // 小红书 token 刷新 API
      const authorizeAccountRefreshApi = 'https://adapi.xiaohongshu.com/api/open/oauth2/refresh_token'

      const response = await axios.post(authorizeAccountRefreshApi, {
        app_id: env.XIAOHONGSHU_CLIENT_KEY,
        secret: env.XIAOHONGSHU_CLIENT_SECRET,
        refresh_token: refreshToken
      })

      logger.info('小红书 token 刷新响应', {
        type: 'token-refresh',
        platform: 'xiaohongshu',
        success: response.data.success,
        code: response.data.code,
        msg: response.data.msg
      })

      if (response.data.success) {
        return {
          success: true,
          data: {
            access_token: response.data.data.access_token,
            refresh_token: response.data.data.refresh_token,
            expires_in: response.data.data.access_token_expires_in,
            scope: response.data.data.scope
          }
        }
      } else {
        return {
          success: false,
          error: `[小红书官方]:${response.data.msg}` || '小红书 token 刷新失败'
        }
      }
    } catch (error) {
      logger.error('小红书 token 刷新 API 调用失败', {
        type: 'token-refresh',
        platform: 'xiaohongshu',
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '小红书 API 调用失败'
      }
    }
  }

  /**
   * 批量刷新 token
   * @param accountIds 账号ID列表
   * @returns 批量刷新结果
   */
  static async batchRefreshTokens(accountIds: string[]): Promise<{
    total: number
    success: number
    failed: number
    skipped: number
    results: Array<{
      accountId: string
      success: boolean
      message: string
      skipped?: boolean
    }>
  }> {
    const results: Array<{
      accountId: string
      success: boolean
      message: string
      skipped?: boolean
    }> = []
    let successCount = 0
    let failedCount = 0
    let skippedCount = 0

    // 首先尝试获取所有账号的锁
    const lockedAccountIds = await this.lockManager.batchAcquireLocks(accountIds)
    const skippedAccountIds = accountIds.filter(id => !lockedAccountIds.includes(id))

    // 记录跳过的账号
    skippedAccountIds.forEach(accountId => {
      results.push({
        accountId,
        success: false,
        message: '账号正在被其他进程刷新，已跳过',
        skipped: true
      })
      skippedCount++
    })

    if (lockedAccountIds.length === 0) {
      logger.warn('所有账号都被锁定，批量刷新跳过', {
        type: 'token-refresh',
        total: accountIds.length,
        skipped: skippedCount
      })
      return {
        total: accountIds.length,
        success: 0,
        failed: 0,
        skipped: skippedCount,
        results
      }
    }

    try {
      // 限制并发数量
      const chunks = this.chunkArray(lockedAccountIds, this.CONCURRENT_REFRESH_LIMIT)

      for (const chunk of chunks) {
        const promises = chunk.map(async (accountId) => {
          const result = await this.refreshAccountToken(accountId)
          return {
            accountId,
            success: result.success,
            message: result.message
          }
        })

        const chunkResults = await Promise.all(promises)
        results.push(...chunkResults)

        // 统计结果
        chunkResults.forEach(result => {
          if (result.success) {
            successCount++
          } else {
            failedCount++
          }
        })
      }
    } finally {
      // 确保释放所有锁
      await this.lockManager.batchReleaseLocks(lockedAccountIds)
    }

    logger.info(`批量刷新完成`, {
      type: 'token-refresh',
      total: accountIds.length,
      success: successCount,
      failed: failedCount,
      skipped: skippedCount
    })

    return {
      total: accountIds.length,
      success: successCount,
      failed: failedCount,
      skipped: skippedCount,
      results
    }
  }

  /**
   * 数组分块工具方法
   */
  private static chunkArray<T>(array: T[], size: number): T[][] {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}

/**
 * Token 刷新定时任务调度器
 * 整合了锁管理、token 刷新服务和调度功能
 */
export class TokenRefreshScheduler {
  private static instance: TokenRefreshScheduler
  private isRunning = false

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): TokenRefreshScheduler {
    if (!this.instance) {
      this.instance = new TokenRefreshScheduler()
    }
    return this.instance
  }

  /**
   * 执行刷新任务的核心逻辑
   */
  async refreshTokens(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Token 刷新任务已在运行，跳过本次执行', {
        type: 'token-refresh-scheduler',
        action: 'skip-running'
      })
      return
    }

    this.isRunning = true
    const startTime = Date.now()

    try {
      logger.info('开始执行 Token 刷新任务', {
        type: 'token-refresh-scheduler',
        action: 'start-execution'
      })

      // 获取需要刷新的账号
      const accountsNeedingRefresh = await TokenRefreshService.getAccountsNeedingRefresh()

      if (accountsNeedingRefresh.length === 0) {
        logger.info('没有需要刷新的账号', {
          type: 'token-refresh-scheduler',
          action: 'no-accounts'
        })
        return
      }

      // 提取账号ID
      const accountIds = accountsNeedingRefresh.map(account => account.id)

      // 批量刷新
      const refreshResult = await TokenRefreshService.batchRefreshTokens(accountIds)

      logger.info('Token 刷新任务执行完成', {
        type: 'token-refresh-scheduler',
        action: 'execution-complete',
        accountsFound: accountsNeedingRefresh.length,
        accountsProcessed: refreshResult.total,
        successCount: refreshResult.success,
        failedCount: refreshResult.failed,
        skippedCount: refreshResult.skipped,
        duration: Date.now() - startTime
      })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'

      logger.error('Token 刷新任务执行失败', {
        type: 'token-refresh-scheduler',
        action: 'execution-failed',
        error: errorMessage,
        duration: Date.now() - startTime
      })
    } finally {
      this.isRunning = false
    }
  }
}

// 导出单例实例
export const tokenRefreshScheduler = TokenRefreshScheduler.getInstance()

import { FastifyInstance } from 'fastify'
import { deviceManager } from '../device-manager'
import { ApiError } from '../utils'
import { logger } from '../services/logger'
import { locationRequestManager } from '../services/socket-response-manager'

// 本地发布的处理
export const localPublishHandler = async (
  clientId: string,
  sessionToken: string,
  data: unknown,
  fastify: FastifyInstance,
) => {
  try {
    // 检查设备是否在线
    const isOnline = await deviceManager.isOnline(clientId)

    if (!isOnline) {
      throw new ApiError(40000, `设备离线，无法进行本地发布, ${clientId}`)
    }

    // 获取设备信息
    const socketId = await deviceManager.getSocketId(clientId)
    if (!socketId) {
      throw new ApiError(40000, `设备信息不存在 ${clientId}`)
    }

    // 准备发送给设备的数据
    const deviceMessage = {
      type: 'task',
      sessionToken,
      data: data,
      timestamp: new Date().toISOString(),
    }

    logger.info('开始推送任务到设备', {
      type: 'publish',
      event: 'task_push',
      clientId,
      socketId,
    })

    fastify.io.to(socketId).emit('task:push', deviceMessage)
  } catch (error) {
    logger.error('本地发布异常', {
      type: 'publish',
      event: 'local_publish_error',
      clientId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}

// 本地地理位置查询
export const localLocationHandler = async (clientId: string, data: unknown, fastify: FastifyInstance) => {
  try {
    // 检查设备是否在线
    const isOnline = await deviceManager.isOnline(clientId)
    if (!isOnline) {
      throw new ApiError(40000, `设备离线，无法进行本地定位, ${clientId}`)
    }
    // 获取设备信息
    const socketId = await deviceManager.getSocketId(clientId)
    if (!socketId) {
      throw new ApiError(40000, `设备信息不存在 ${clientId}`)
    }

    // 生成请求ID（包含实例信息）
    const requestId = locationRequestManager.generateRequestId(clientId)
    // 准备发送给设备的数据
    const deviceMessage = {
      type: 'location',
      data: data,
      timestamp: new Date().toISOString(),
      requestId, // 添加请求ID
    }
    logger.info('开始推送定位任务到设备', {
      type: 'location',
      event: 'location_push',
      clientId,
      socketId,
      requestId,
    })

    // 先设置响应监听，再发送请求
    const responsePromise = locationRequestManager.waitForResponse(
      requestId,
      clientId,
      30000, // 30秒超时
    )
    // 发送消息到设备（不使用 ack）
    fastify.io.to(socketId).emit('location', deviceMessage)

    // 等待响应
    const response = await responsePromise

    logger.info('定位任务完成', {
      type: 'location',
      event: 'location_complete',
      clientId,
      socketId,
      requestId,
    })

    return response
  } catch (error) {
    logger.error('本地定位异常', {
      type: 'location',
      event: 'local_location_error',
      clientId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    })
    throw error
  }
}

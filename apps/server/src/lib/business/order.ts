import { db, Prisma } from '@coozf/db'
import { OrderListParams, PaginationParams } from '@coozf/zod'

/**
 * 订单业务逻辑服务
 */
export class OrderService {
  /**
   * 生成订单号
   */
  static generateOrderNo(): string {
    const timestamp = Date.now().toString()
    const random = Math.random().toString().slice(2, 8)
    return `ORD${timestamp}${random}`
  }

  /**
   * 获取订单列表
   */
  static async getOrderList(params: OrderListParams & PaginationParams & { userId?: string }) {
    const { page = 1, pageSize = 10, status, type, startDate, endDate, search, applicationId, userId } = params

    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.OrderWhereInput = {}
    if (userId) where.application = { userId }
    if (applicationId) where.applicationId = applicationId
    if (status) where.status = status
    if (type) where.type = type
    if (search) {
      where.orderNo = {
        contains: search,
      }
    }
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    // 获取总数
    const total = await db.order.count({ where })

    // 获取订单列表
    const orderList = await db.order.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        application: {
          select: {
            name: true,
            appId: true,
          },
        },
      },
    })

    return {
      data: orderList.map((order) => ({
        ...order,
        amount: order.amount.toNumber(),
        quotaAmount: order.quotaAmount.toNumber(),
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 获取订单详情
   */
  static async getOrderById(orderId: string, userId?: string) {
    return await db.order.findFirst({
      where: {
        id: orderId,
        application: {
          userId,
        },
      },
    })
  }

  /**
   * 更新订单状态
   */
  static async updateOrderStatus(
    orderId: string,
    status: 'PENDING' | 'COMPLETED' | 'CANCELLED',
    remarks?: string,
  ): Promise<void> {
    const updateData: Prisma.OrderUpdateInput = { status }
    if (remarks) updateData.remarks = remarks

    await db.order.update({
      where: { id: orderId },
      data: updateData,
    })
  }

  /**
   * 申请发票
   */
  static async requestInvoice(orderId: string): Promise<void> {
    await db.order.update({
      where: { id: orderId },
      data: { invoiceRequested: true },
    })
  }

  /**
   * 取消订单
   */
  static async cancelOrder(orderId: string): Promise<void> {
    await this.updateOrderStatus(orderId, 'CANCELLED', '订单已取消')
  }

  /**
   * 处理订单完成时的配额分配
   */
  static async processOrderCompletion(orderId: string): Promise<void> {
    let applicationId: string | null = null
    
    await db.$transaction(async (tx) => {
      const order = await tx.order.findUnique({
        where: { id: orderId },
        include: {
          application: {
            select: {
              id: true,
            },
          },
        },
      })

      if (!order) {
        throw new Error('订单不存在')
      }

      if (order.status !== 'COMPLETED') {
        throw new Error('只有已完成的订单才能处理配额分配')
      }

      applicationId = order.applicationId
      const quotaAmount = order.quotaAmount.toNumber()

      // 根据配额类型分配配额
      if (order.quotaType === 'ACCOUNT') {
        await tx.application.update({
          where: { id: order.applicationId },
          data: {
            accountQuota: {
              increment: quotaAmount,
            },
          },
        })
      } else if (order.quotaType === 'TRAFFIC') {
        await tx.application.update({
          where: { id: order.applicationId },
          data: {
            trafficQuotaGB: {
              increment: order.quotaAmount,
            },
          },
        })
      }
    })
  }

  /**
   * 获取应用配额订单历史
   */
  static async getQuotaOrderHistory(applicationId: string, page: number = 1, pageSize: number = 10) {
    const skip = (page - 1) * pageSize

    const orders = await db.order.findMany({
      where: {
        applicationId,
        status: 'COMPLETED',
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        application: {
          select: {
            name: true,
            appId: true,
          },
        },
      },
    })

    const total = await db.order.count({
      where: {
        applicationId,
        status: 'COMPLETED',
      },
    })

    return {
      data: orders.map((order) => ({
        ...order,
        amount: order.amount.toNumber(),
        quotaAmount: order.quotaAmount.toNumber(),
      })),
      total,
      page,
      pageSize,
    }
  }
}

import crypto from 'crypto'
import { Application, db } from '@coozf/db'
import { TRPCError } from '@trpc/server'
import { CreateVideoTaskInput, PaginationParams } from '@coozf/zod'
import { VideoUtils } from '../utils/video'
import { logger } from '../services/logger'

/**
 * 发布记录业务逻辑
 */
export class PublishService {
  /**
   * 生成发布ID
   * 格式：pub_ + 16位随机字符串
   */
  static generatePublishId(): string {
    const randomString = crypto.randomBytes(8).toString('hex')
    return `pub_${randomString}`
  }

  /**
   * 生成任务ID
   * 格式：task_ + 16位随机字符串
   */
  static generateTaskId(): string {
    const randomString = crypto.randomBytes(8).toString('hex')
    return `task_${randomString}`
  }

  /**
   * 创建发布记录
   * @param applicationId 应用ID
   * @param tasks 任务列表 (已拆分好的任务)
   * @returns 发布记录和任务详情
   */
  static async createPublishRecord(applicationId: string, tasks: Array<CreateVideoTaskInput>) {
    // 根据url计算流量
    const trafficAmounts = await VideoUtils.getBatchVideoSizesFromUrls(tasks.map((task) => task.publishData.video.url))
    // 1. 计算总流量
    const totalTraffic = trafficAmounts.reduce((sum, num) => sum + num, 0)

    // 2. 使用事务进行原子操作：检查配额 + 预扣除 + 创建记录
    const result = await db.$transaction(async (tx) => {
      // 2.1 在事务中重新检查配额（防止并发问题）
      const application = await db.application.findUnique({
        where: {
          id: applicationId,
        },
        select: {
          trafficUsedGB: true,
          trafficQuotaGB: true,
        },
      })

      if (!application) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '应用不存在',
        })
      }

      const usedPlusRequired = application.trafficUsedGB.add(totalTraffic)
      if (usedPlusRequired.gt(application.trafficQuotaGB)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `流量配额不足。当前已使用: ${application.trafficUsedGB.toNumber().toFixed(3)}GB, 需要: ${totalTraffic.toFixed(3)}GB, 总配额: ${application.trafficQuotaGB.toNumber().toFixed(3)}GB`,
        })
      }

      // 2.2 预扣除流量（防止并发超额）
      await tx.application.update({
        where: { id: applicationId },
        data: {
          trafficUsedGB: {
            increment: totalTraffic,
          },
        },
      })

      // 2.3 创建发布记录
      const publishRecord = await tx.publishRecord.create({
        data: {
          applicationId,
          publishId: this.generatePublishId(),
          estimatedTraffic: totalTraffic,
          actualTraffic: 0,
          accountCount: tasks.length,
          pendingCount: tasks.length,
          successCount: 0,
          failedCount: 0,
          status: 'PENDING',
          metadata: {
            title: tasks[0]?.title,
            desc: tasks[0]?.desc,
            cover: tasks[0]?.publishData.covers[0]?.pathOrUrl,
          },
        },
      })

      // 2.4 创建流量使用明细
      const trafficDetails = tasks.map((task, index) => ({
        publishRecordId: publishRecord.id,
        applicationId,
        authAccountId: task.authorInfo.id,
        taskId: task.taskId,
        estimatedTraffic: trafficAmounts[index]!,
        actualTraffic: 0,
        status: 'PENDING' as const,
        metadata: {
          title: task.title,
          desc: task.desc,
          cover: task.publishData.covers[0]?.pathOrUrl,
          video: task.publishData.video.url,
        },
      }))

      await tx.trafficUsage.createMany({
        data: trafficDetails,
      })

      return publishRecord
    })

    return result
  }

  /**
   * 获取发布记录列表
   * @param applicationId 应用ID
   * @param page 页码
   * @param limit 每页数量
   * @returns 发布记录列表
   */
  static async getPublishRecords(params: PaginationParams & { applicationId: string }) {
    const { page, pageSize, applicationId } = params
    const [records, total] = await Promise.all([
      db.publishRecord.findMany({
        where: { applicationId },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      db.publishRecord.count({
        where: { applicationId },
      }),
    ])

    return {
      data: records.map((record) => ({
        ...record,
        estimatedTraffic: record.estimatedTraffic.toNumber(),
        actualTraffic: record.actualTraffic.toNumber(),
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 获取发布记录详情
   * @param publishId 发布ID
   * @returns 发布记录详情
   */
  static async getPublishRecordDetail(publishId: string) {
    const record = await db.publishRecord.findUnique({
      where: { publishId },
      include: {
        trafficDetails: {
          include: {
            authAccount: {
              select: {
                id: true,
                platformCode: true,
                platformUserName: true,
                platformAvatar: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    })

    if (!record) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '发布记录不存在',
      })
    }

    return record
  }

  /**
   * 处理Webhook回调 - 更新单个任务状态
   * @param taskId 任务ID
   * @param success 是否成功
   * @param errorMessage 错误消息
   */
  static async handleTaskCallback(taskId: string, publishRecordId: string, success: boolean, errorMessage?: string) {
    let applicationId: string | null = null

    const result = await db.$transaction(async (tx) => {
      // 1. 获取任务信息
      const task = await tx.trafficUsage.findUnique({
        where: { taskId, publishRecordId },
        include: { publishRecord: true },
      })

      if (!task || task.status !== 'PENDING') {
        return
      }

      applicationId = task.applicationId

      // 2. 准备更新数据
      const taskUpdate = success
        ? {
            status: 'SUCCESS' as const,
            actualTraffic: task.estimatedTraffic,
          }
        : {
            status: 'FAILED' as const,
            actualTraffic: 0,
            errorMessage,
          }

      const recordUpdate = success
        ? {
            successCount: { increment: 1 },
            pendingCount: { decrement: 1 },
            actualTraffic: { increment: task.estimatedTraffic },
          }
        : {
            failedCount: { increment: 1 },
            pendingCount: { decrement: 1 },
          }

      // 3. 并发执行主要更新操作
      const [, updatedRecord] = await Promise.all([
        tx.trafficUsage.update({
          where: { taskId },
          data: taskUpdate,
        }),
        tx.publishRecord.update({
          where: { id: task.publishRecordId },
          data: recordUpdate,
        }),
      ])

      // 4. 如果失败，需要回退流量
      if (!success) {
        await tx.application.update({
          where: { id: task.applicationId },
          data: {
            trafficUsedGB: {
              decrement: task.estimatedTraffic,
            },
          },
        })
      }

      // 5. 检查是否所有任务都完成，更新发布记录最终状态
      if (updatedRecord.pendingCount === 0) {
        const finalStatus =
          updatedRecord.failedCount === 0 ? 'COMPLETED' : updatedRecord.successCount === 0 ? 'FAILED' : 'PARTIAL'

        await tx.publishRecord.update({
          where: { id: task.publishRecordId },
          data: { status: finalStatus },
        })
      }

      return task
    })

    return result
  }

  /**
   * 取消发布记录并回退预扣除的流量
   * @param publishId 发布ID
   * @param reason 取消原因
   * @returns 取消结果
   */
  static async cancelPublishRecord(publishId: string, reason: string = '用户取消') {
    let applicationId: string | null = null

    const result = await db.$transaction(async (tx) => {
      // 1. 获取发布记录
      const publishRecord = await tx.publishRecord.findUnique({
        where: { publishId },
        include: {
          trafficDetails: true,
        },
      })

      if (!publishRecord) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '发布记录不存在',
        })
      }

      if (publishRecord.status !== 'PENDING') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '只能取消待处理状态的发布记录',
        })
      }

      applicationId = publishRecord.applicationId

      // 2. 计算需要回退的流量
      let totalRefundTraffic = 0
      const pendingTasks = publishRecord.trafficDetails.filter((task) => task.status === 'PENDING')

      if (pendingTasks.length > 0) {
        totalRefundTraffic = pendingTasks.reduce((sum, task) => sum + task.estimatedTraffic.toNumber(), 0)

        // 3. 回退应用流量配额（新版本都会预扣除）
        await tx.application.update({
          where: { id: publishRecord.applicationId },
          data: {
            trafficUsedGB: {
              decrement: totalRefundTraffic,
            },
          },
        })
      }

      // 4. 取消所有待处理的任务
      await tx.trafficUsage.updateMany({
        where: {
          publishRecordId: publishRecord.id,
          status: 'PENDING',
        },
        data: {
          status: 'FAILED',
          actualTraffic: 0,
          errorMessage: reason,
        },
      })

      // 5. 更新发布记录状态
      await tx.publishRecord.update({
        where: { id: publishRecord.id },
        data: {
          status: 'FAILED',
          failedCount: publishRecord.pendingCount,
          pendingCount: 0,
        },
      })

      return {
        publishId,
        refundedTraffic: totalRefundTraffic,
        cancelledTasksCount: pendingTasks.length,
        reason,
      }
    })
    return result
  }

  /**
   * 超时自动失败任务 - 简单版本
   * @param timeoutMinutes 超时时间（分钟），默认30分钟
   * @returns 处理的任务数量
   */
  static async autoFailTimeoutTasks(timeoutMinutes: number = 30): Promise<number> {
    const timeoutDate = new Date(Date.now() - timeoutMinutes * 60 * 1000)
    const affectedApplicationIds = new Set<string>()

    const processedCount = await db.$transaction(async (tx) => {
      // 1. 找到超时的待处理任务
      const timeoutTasks = await tx.trafficUsage.findMany({
        where: {
          status: 'PENDING',
          createdAt: {
            lt: timeoutDate,
          },
        },
        include: {
          publishRecord: true,
        },
      })

      if (timeoutTasks.length === 0) {
        return 0
      }

      let processedCount = 0

      for (const task of timeoutTasks) {
        // 收集受影响的应用ID
        affectedApplicationIds.add(task.applicationId)

        // 2. 标记任务为失败并回退流量
        await tx.trafficUsage.update({
          where: { id: task.id },
          data: {
            status: 'FAILED',
            actualTraffic: 0,
            errorMessage: `任务超时自动失败（${timeoutMinutes}分钟）`,
          },
        })

        // 3. 回退预扣除的流量
        await tx.application.update({
          where: { id: task.applicationId },
          data: {
            trafficUsedGB: {
              decrement: task.estimatedTraffic,
            },
          },
        })

        // 4. 更新发布记录统计
        await tx.publishRecord.update({
          where: { id: task.publishRecordId },
          data: {
            failedCount: { increment: 1 },
            pendingCount: { decrement: 1 },
          },
        })

        processedCount++
      }

      // 5. 更新已完成的发布记录状态
      const completedRecords = await tx.publishRecord.findMany({
        where: {
          id: {
            in: timeoutTasks.map((t) => t.publishRecordId),
          },
          pendingCount: 0,
        },
      })

      for (const record of completedRecords) {
        const finalStatus = record.failedCount === 0 ? 'COMPLETED' : record.successCount === 0 ? 'FAILED' : 'PARTIAL'

        await tx.publishRecord.update({
          where: { id: record.id },
          data: { status: finalStatus },
        })
      }

      logger.info('处理超时任务完成', {
        type: 'business',
        operation: 'auto_fail_timeout',
        processedCount,
        affectedApplications: affectedApplicationIds.size,
      })
      return processedCount
    })

    return processedCount
  }

  /**
   * 获取应用的发布统计
   * @param applicationId 应用ID
   * @returns 发布统计数据
   */
  static async getPublishStats(applicationId: string) {
    const stats = await db.publishRecord.aggregate({
      where: { applicationId },
      _count: { id: true },
      _sum: {
        actualTraffic: true,
        successCount: true,
        failedCount: true,
      },
    })

    const statusStats = await db.publishRecord.groupBy({
      by: ['status'],
      where: { applicationId },
      _count: { id: true },
    })

    return {
      totalRecords: stats._count.id,
      totalTraffic: stats._sum.actualTraffic || 0,
      totalSuccess: stats._sum.successCount || 0,
      totalFailed: stats._sum.failedCount || 0,
      statusBreakdown: statusStats.reduce(
        (acc: Record<string, number>, stat) => {
          acc[stat.status] = stat._count.id
          return acc
        },
        {} as Record<string, number>,
      ),
    }
  }
}

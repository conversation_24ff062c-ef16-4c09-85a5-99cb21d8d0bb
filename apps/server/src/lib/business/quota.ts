import { db, Prisma, Application } from '@coozf/db'
import type { QuotaOverview } from '../types/quota'

/**
 * 配额业务逻辑服务
 */
export class QuotaService {
  /**
   * 获取应用配额信息
   */
  static async getApplicationQuota(applicationId: string) {
    const application = await db.application.findUnique({
      where: { id: applicationId },
      select: {
        accountQuota: true,
        trafficQuotaGB: true,
        trafficUsedGB: true,
        accountQuotaExpireDate: true,
      },
    })

    if (!application) {
      throw new Error('应用不存在')
    }

    return {
      accountQuota: application.accountQuota || 0,
      trafficQuotaGB: application.trafficQuotaGB || new Prisma.Decimal(0),
      trafficUsedGB: application.trafficUsedGB || new Prisma.Decimal(0),
      accountQuotaExpireDate: application.accountQuotaExpireDate,
    }
  }

  /**
   * 获取应用已使用的账号数量
   */
  static async getApplicationAccountUsage(applicationId: string): Promise<number> {
    return await db.authAccount.count({
      where: { applicationId, isDeleted: false },
    })
  }

  /**
   * 检查账号配额是否足够
   */
  static async checkAccountQuota(applicationId: string, requiredAccounts: number): Promise<boolean> {
    const quota = await this.getApplicationQuota(applicationId)
    const currentUsage = await this.getApplicationAccountUsage(applicationId)

    // 检查配额是否过期
    if (quota.accountQuotaExpireDate && new Date() > quota.accountQuotaExpireDate) {
      return false
    }

    return currentUsage + requiredAccounts <= quota.accountQuota
  }

  /**
   * 检查流量配额是否足够
   */
  static async checkTrafficQuota(applicationId: string, requiredTrafficGB: number): Promise<boolean> {
    const quota = await this.getApplicationQuota(applicationId)
    const requiredDecimal = new Prisma.Decimal(requiredTrafficGB)
    const usedPlusRequired = quota.trafficUsedGB.add(requiredDecimal)
    return usedPlusRequired.lte(quota.trafficQuotaGB)
  }

  /**
   * 消费流量配额
   */
  static async consumeTrafficQuota(applicationId: string, trafficGB: number, endpoint: string): Promise<boolean> {
    const trafficDecimal = new Prisma.Decimal(trafficGB)

    // 检查配额是否足够
    const canConsume = await this.checkTrafficQuota(applicationId, trafficGB)
    if (!canConsume) {
      return false
    }

    // 使用事务更新流量使用量并记录API调用
    await db.$transaction(async (tx) => {
      // 更新应用流量使用量
      await tx.application.update({
        where: { id: applicationId },
        data: {
          trafficUsedGB: {
            increment: trafficDecimal,
          },
        },
      })

      // 记录API调用
      await tx.apiCall.create({
        data: {
          applicationId,
          endpoint,
          method: 'POST',
          costType: 'TRAFFIC',
          costAmount: trafficDecimal,
          statusCode: 200,
        },
      })
    })

    return true
  }

  /**
   * 增加账号配额
   */
  static async addAccountQuota(applicationId: string, additionalQuota: number): Promise<void> {
    await db.application.update({
      where: { id: applicationId },
      data: {
        accountQuota: {
          increment: additionalQuota,
        },
      },
    })
  }

  /**
   * 增加流量配额
   */
  static async addTrafficQuota(applicationId: string, additionalQuotaGB: number): Promise<void> {
    const quotaDecimal = new Prisma.Decimal(additionalQuotaGB)
    await db.application.update({
      where: { id: applicationId },
      data: {
        trafficQuotaGB: {
          increment: quotaDecimal,
        },
      },
    })
  }

  /**
   * 获取配额使用概览
   */
  static async getQuotaOverview(applicationId: string): Promise<QuotaOverview> {
    const quota = await this.getApplicationQuota(applicationId)
    const accountUsage = await this.getApplicationAccountUsage(applicationId)

    const trafficQuotaNumber = quota.trafficQuotaGB.toNumber()
    const trafficUsedNumber = quota.trafficUsedGB.toNumber()
    const trafficAvailable = quota.trafficQuotaGB.sub(quota.trafficUsedGB).toNumber()

    return {
      account: {
        quota: quota.accountQuota,
        used: accountUsage,
        available: quota.accountQuota - accountUsage,
        expireDate: quota.accountQuotaExpireDate,
        expired: quota.accountQuotaExpireDate ? new Date() > quota.accountQuotaExpireDate : false,
      },
      traffic: {
        quotaGB: trafficQuotaNumber,
        usedGB: trafficUsedNumber,
        availableGB: trafficAvailable,
        usagePercent: trafficQuotaNumber > 0 ? (trafficUsedNumber / trafficQuotaNumber) * 100 : 0,
      },
    }
  }

  // 判断应用账号是否有足够的配额
  static async hasSufficientAccountQuota(
    applicationId: string,
    requiredAccounts: number,
    quota?: Pick<Application, 'accountQuota' | 'accountQuotaExpireDate'>,
  ): Promise<boolean> {
    const currentUsage = await this.getApplicationAccountUsage(applicationId)
    if (!quota) {
      quota = await this.getApplicationQuota(applicationId)
    }

    // 检查配额是否过期
    if (quota.accountQuotaExpireDate && new Date() > quota.accountQuotaExpireDate) {
      return false
    }

    return currentUsage + requiredAccounts <= quota.accountQuota
  }
}

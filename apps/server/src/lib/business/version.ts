import { db } from '@coozf/db'
import { Prisma, VersionType, Platform } from '@prisma/client'
import { CreateVersionInput, CheckUpdateParams, CheckUpdateRes } from '@coozf/zod'
import { adminOssService } from '../services/oss'
import { messagePublisher } from '../services/message-publisher'
import { TRPCError } from '@trpc/server'
import semver from 'semver'
import { env } from '@/env'

/**
 * 版本管理业务逻辑
 */
export class VersionService {
  /**
   * 创建新版本
   */
  static async createVersion(data: CreateVersionInput, publishedId: string) {
    // 验证版本号格式（简单的正则验证）
    const { version, platform, type, description, forceUpdate, fileUrl } = data
    if (!semver.valid(version)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '无效的版本号格式',
      })
    }

    const latestVersion = await db.version.findFirst({
      where: { isActive: true, platform, type },
      orderBy: { createdAt: 'desc' },
    })

    if (latestVersion && semver.gte(latestVersion.version, version)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '新版本号必须大于当前最新版本号',
      })
    }

    let finalDownloadUrl: string

    if (data.type === 'DESKTOP') {
      // 桌面端：检查 OSS 资源是否存在并生成下载地址
      if (data.platform === 'WIN' || data.platform === 'MAC') {
        await this.checkOssResourceExists(data.version, data.platform)
        finalDownloadUrl = this.generateDesktopDownloadUrl(data.version)
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '该版本资源未构建完成',
        })
      }
    } else {
      if (fileUrl) {
        // 通过文件id 获取url
        finalDownloadUrl = fileUrl
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '文件id不能为空',
        })
      }
    }
    const newVersion = await db.version.create({
      data: {
        version: version,
        type: type,
        platform: type === 'DESKTOP' ? platform : null,
        downloadUrl: finalDownloadUrl,
        forceUpdate: forceUpdate,
        description: description,
        publishedId,
      },
    })

    // 版本创建成功后，发布更新消息到 Redis
    try {
      await messagePublisher.publishVersionUpdate({
        type: type,
        version: version,
        forceUpdate: forceUpdate,
        description: description,
        downloadUrl: finalDownloadUrl,
        timestamp: Date.now(),
        publishedBy: publishedId,
      })
    } catch (error) {
      // 记录错误但不影响版本创建的成功响应
      console.error('发布版本更新消息失败:', error)
    }

    return newVersion
  }

  /**
   * 获取版本列表（支持分页和筛选）
   */
  static async getVersionList(params: { page: number; pageSize: number; type?: VersionType; platform?: Platform }) {
    const { page, pageSize, type, platform } = params
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: Prisma.VersionWhereInput = {}

    if (type) {
      where.type = type
    }
    if (platform) {
      where.platform = platform
    }

    // 获取总数
    const total = await db.version.count({ where })

    // 获取分页数据
    const versions = await db.version.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: { publishedAt: 'desc' },
      include: {
        admin_user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    })

    return {
      items: versions,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 更新版本信息
   */
  static async updateVersion(
    id: string,
    data: {
      downloadUrl?: string
      forceUpdate?: boolean
      description?: string
      isActive?: boolean
    },
  ) {
    return await db.version.update({
      where: { id },
      data,
    })
  }

  /**
   * 删除版本（软删除）
   */
  static async deleteVersion(id: string) {
    return await db.version.delete({
      where: { id },
    })
  }

  /**
   * 检查是否需要更新
   */
  static async checkForUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    const { type, platform, version, appId } = params

    // 当传入appId且type为DESKTOP时，查询ClientVersion表
    if (appId && type === 'DESKTOP') {
      return await this.checkClientVersionUpdate(params)
    }

    // 否则查询原有Version表（公用版本）
    return await this.checkPublicVersionUpdate(params)
  }

  /**
   * 检查公用版本更新
   */
  private static async checkPublicVersionUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    const { type, platform, version } = params
    const where: Prisma.VersionWhereInput = {
      type,
      isActive: true,
    }

    // 桌面端需要指定平台
    if (type === 'DESKTOP') {
      where.platform = platform
    }

    const latestVersion = await db.version.findFirst({
      where,
      orderBy: { publishedAt: 'desc' },
    })

    if (!latestVersion) {
      return {
        hasUpdate: false,
        latestVersion: null,
        forceUpdate: false,
      }
    }

    const { downloadUrl, forceUpdate, description, version: latestVersionNumber } = latestVersion
    const hasUpdate = !version || semver.gt(latestVersion.version, version)

    if (!hasUpdate) {
      return {
        hasUpdate: false,
        forceUpdate: false,
        latestVersion: {
          downloadUrl,
          forceUpdate,
          description,
          version: latestVersionNumber,
        },
      }
    }
    const forceUpdateVersions = await db.version.findMany({
      where: {
        forceUpdate: true,
        version: {
          gt: version,
          lte: latestVersionNumber,
        },
      },
      orderBy: { createdAt: 'asc' },
    })

    return {
      hasUpdate: true,
      forceUpdate: forceUpdateVersions.length > 0,
      latestVersion: {
        downloadUrl: latestVersion.downloadUrl,
        forceUpdate: latestVersion.forceUpdate,
        description: latestVersion.description,
        version: latestVersionNumber,
      },
    }
  }

  /**
   * 检查 OSS 资源是否存在（仅支持 Windows 和 macOS）
   */
  private static async checkOssResourceExists(version: string, platform: Platform) {
    // 根据环境生成检查路径
    const envPrefix = env.MODE === 'prod' ? 'production-open' : 'staging-open'
    const checkUrl = `${envPrefix}/${version}/`

    try {
      // 获取 OSS 文件列表
      const result = await adminOssService.getListObjects(checkUrl)
      const existingFiles = result.data.Contents?.map((obj: any) => obj.Key) || []

      if (existingFiles.length <= 0) {
        throw new Error('该版本未构建')
      }

      // 根据平台检查必需文件
      let requiredFiles: string[] = []

      if (platform === 'WIN') {
        requiredFiles = ['win32-x64.yml', 'win32-ia32.yml']
      } else if (platform === 'MAC') {
        requiredFiles = ['darwin-x64-mac.yml', 'darwin-arm64-mac.yml']
      }

      // 检查所有必需文件是否存在
      const missingFiles = requiredFiles.filter((fileName) => !existingFiles.includes(checkUrl + fileName))

      if (missingFiles.length > 0) {
        throw new Error(`该版本未全部构建，缺少文件: ${missingFiles.join(', ')}`)
      }
    } catch (error) {
      throw new Error(`资源检查失败: ${error instanceof Error ? error.message : error}`)
    }
  }

  /**
   * 生成桌面端下载地址
   */
  private static generateDesktopDownloadUrl(version: string): string {
    const envPrefix = process.env.NODE_ENV === 'production' ? 'production-open' : 'staging-open'
    const baseUrl = process.env.OSS_DOWNLOAD_DESKTOP_URL || ''
    return `${baseUrl}/${envPrefix}/${version}/`
  }

  /**
   * 上传浏览器插件到 OSS
   */
  private static async uploadBrowserPluginToOss(version: string, fileBuffer: Buffer): Promise<string> {
    try {
      const envPrefix = process.env.NODE_ENV === 'production' ? '' : `${process.env.NODE_ENV}/`
      const fileName = `browser-plugin/${envPrefix}browser-plugin-${version}.zip`

      // 上传文件到 OSS
      await adminOssService.uploadFile(fileBuffer, fileName)

      // 生成下载地址
      const baseUrl = process.env.OSS_DOWNLOAD_URL || ''
      return `${baseUrl}/${fileName}`
    } catch (error) {
      throw new Error(`浏览器插件上传失败: ${error instanceof Error ? error.message : error}`)
    }
  }

  /**
   * 检查客户端版本更新（OEM版本）
   */
  private static async checkClientVersionUpdate(params: CheckUpdateParams): Promise<CheckUpdateRes> {
    const { appId, type, platform, version } = params

    if (!appId) {
      // 如果没有appId，回退到公用版本检查
      return await this.checkPublicVersionUpdate(params)
    }

    // 查询最新的成功构建的客户端版本
    const latestClientVersion = await db.clientVersion.findFirst({
      where: {
        applicationId: appId,
        platform: platform,
        buildStatus: 'SUCCESS',
      },
      orderBy: { createdAt: 'desc' },
      include: {
        baseVersion: {
          select: {
            id: true,
            version: true,
          },
        },
      },
    })

    if (!latestClientVersion) {
      // 如果没有客户端版本，回退到公用版本检查
      return await this.checkPublicVersionUpdate(params)
    }

    // 版本比较逻辑
    const hasUpdate = !version || semver.gt(latestClientVersion.baseVersion.version, version)

    if (!hasUpdate) {
      return {
        hasUpdate: false,
        forceUpdate: false,
        latestVersion: {
          downloadUrl: latestClientVersion.downloadUrl || '',
          forceUpdate: false,
          description: latestClientVersion.description,
          version: latestClientVersion.baseVersion.version,
        },
      }
    }

    return {
      hasUpdate: true,
      forceUpdate: false, // 客户端版本暂不支持强制更新
      latestVersion: {
        downloadUrl: latestClientVersion.downloadUrl || '',
        forceUpdate: false,
        description: latestClientVersion.description,
        version: latestClientVersion.baseVersion.version,
      },
    }
  }
}

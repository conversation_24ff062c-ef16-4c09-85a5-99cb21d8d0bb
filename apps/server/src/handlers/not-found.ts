import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { ResponseWrapper } from '@/lib'

export const setupNotFoundHandler = (app: FastifyInstance) => {
  app.setNotFoundHandler(async (request: FastifyRequest, reply: FastifyReply) => {
    if (request.url.startsWith('/api/')) {
      return reply.status(404).send(ResponseWrapper.error(40400, 'API route not found'))
    }
    return reply.sendFile('index.html')
  })
} 
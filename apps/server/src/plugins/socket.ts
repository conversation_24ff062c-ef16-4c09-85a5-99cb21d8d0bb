import type { FastifyInstance, FastifyPluginAsync } from 'fastify'
import { Server } from 'socket.io'
import type { Server as SocketIOServer } from 'socket.io'
import { deviceManager } from '../lib/device-manager'
import { messageSubscriber } from '../lib/services/message-subscriber'
import fp from 'fastify-plugin'
import { cacheService, logger } from '@/lib'
import { createAdapter } from '@socket.io/redis-adapter'
import { locationRequestManager } from '@/lib/services/socket-response-manager'

declare module 'fastify' {
  interface FastifyInstance {
    io: SocketIOServer
  }
}

// 使用 Fastify 插件模式
export const socketPlugin: FastifyPluginAsync = async (fastify) => {
  // 在 onReady 钩子中初始化 Socket.IO
  fastify.addHook('onReady', async () => {
    const io = new Server(fastify.server, {
      path: '/socket.io/',
      transports: ['websocket'],
      cors: { origin: '*' },
      allowUpgrades: false,
    })

    // 使用现有的 Redis 实例配置 Redis Adapter
    try {
      const redisInstance = cacheService.getRedisInstance()

      // 为 Socket.IO 创建独立的 pub/sub 客户端
      const pubClient = redisInstance.duplicate()
      const subClient = redisInstance.duplicate()

      io.adapter(createAdapter(pubClient, subClient))

      logger.info('Socket.IO Redis Adapter配置成功', {
        type: 'socket',
        adapter: 'redis',
        multiNode: true,
      })

      // 清理资源
      fastify.addHook('onClose', async () => {
        pubClient.disconnect()
        subClient.disconnect()
      })
    } catch (error) {
      logger.warn('Socket.IO Redis Adapter配置失败，运行在单节点模式', {
        type: 'socket',
        adapter: 'memory',
        multiNode: false,
        error: error instanceof Error ? error.message : error,
      })
    }

    // 使用 decorate 添加 io 实例
    fastify.decorate('io', io)

    // 设置消息订阅器的应用实例并启动订阅
    messageSubscriber.setApp(fastify)
    await messageSubscriber.start()

    io.on('connection', (socket) => {
      logger.info('设备连接', {
        type: 'socket',
        event: 'connection',
        socketId: socket.id,
      })

      socket.on('device:register', async (deviceId: string, ack?: Function) => {
        if (!deviceId) {
          ack?.({ success: false, error: '设备ID不能为空' })
          return
        }

        try {
          await deviceManager.connect(deviceId, socket.id)
          socket.join(`device:${deviceId}`)
          socket.data.deviceId = deviceId

          ack?.({ success: true, deviceId })
        } catch (error) {
          logger.error('设备注册失败', {
            type: 'socket',
            event: 'device:register',
            socketId: socket.id,
            deviceId,
            error: error instanceof Error ? error.message : error,
          })
          ack?.({ success: false, error: '注册失败' })
        }
      })

      // 监听设备的定位响应
      socket.on('location_response', (data: { requestId: string; success: boolean; data?: any; error?: string }) => {
        logger.info('收到设备定位响应', {
          type: 'location',
          event: 'location_response_received',
          socketId: socket.id,
          requestId: data.requestId,
          success: data.success,
        })

        if (data.success) {
          locationRequestManager.handleResponse(data.requestId, {
            success: true,
            data: data.data,
          })
        } else {
          locationRequestManager.handleResponse(data.requestId, {
            success: false,
            error: data.error || '定位失败',
          })
        }
      })

      socket.on('disconnect', async () => {
        const deviceId = socket.data.deviceId
        if (deviceId) {
          try {
            await deviceManager.disconnect(deviceId)
          } catch (error) {
            logger.error('设备断开失败', {
              type: 'socket',
              event: 'disconnect',
              socketId: socket.id,
              deviceId,
              error: error instanceof Error ? error.message : error,
            })
          }
        }
      })
    })
    await locationRequestManager.initialize(fastify)

    fastify.addHook('onClose', async () => {
      await locationRequestManager.cleanup()
      await io.close()
    })
  })
}

// 使用 fastify-plugin 包装，确保装饰器在正确的时机添加
export default fp(socketPlugin, {
  name: 'socket-plugin',
  fastify: '5.x',
})

// 注册插件的函数
export const registerSocketPlugin = async (app: FastifyInstance) => {
  await app.register(socketPlugin)
}

import type { FastifyInstance } from 'fastify'
import type { FastifyTRPCPluginOptions } from '@trpc/server/adapters/fastify'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'
import { fastifyTRPCOpenApiPlugin } from 'trpc-to-openapi'
import fastifyCookie from '@fastify/cookie'
import fastifyStatic from '@fastify/static'
import path from 'path'
import { authHandler } from '@coozf/auth'

import type { AppRouter } from '../router'
import { appRouter } from '../router'
import createContext from '../context'
import { isDev } from '../env'
import routes from '../fastify-router'
import { openApiDocument } from '../openapi'
import { openApiRouter } from '@/router/open-api'
import { socketPlugin } from './socket'

const createPublicContext = async (opts: any) => {
  const context = createContext(opts)
  return { ...context, isPublicApi: true }
}

export const registerPlugins = async (app: FastifyInstance) => {
  // 注册 cookie 插件
  await app.register(fastifyCookie)

  // 注册静态文件服务
  await app.register(fastifyStatic, {
    root: path.join(process.cwd(), 'public'),
    prefix: '/',
  })

  app.route({
    method: ['GET', 'POST'],
    url: '/api/auth/*',
    handler: authHandler,
  })

  await app.register(socketPlugin)
  // 注册 tRPC 插件
  await app.register(fastifyTRPCPlugin, {
    prefix: '/api/trpc',
    useWSS: false,
    trpcOptions: {
      router: appRouter,
      createContext,
      onError({ path, error }) {
        console.error(`Error in tRPC handler on path '${path}':`, error)
      },
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
  })

  // 注册 OpenAPI 插件
  await app.register(fastifyTRPCOpenApiPlugin, {
    basePath: '/api/open',
    router: openApiRouter,
    createContext: createPublicContext,
    onError({ error } : any) {
      throw error
    },
  })

  // 注册 OpenAPI 文档路由
  app.get('/openapi.json', () => openApiDocument)

  // 注册应用路由
  await app.register(routes, { prefix: '/api' })

  // 开发环境下的调试面板
  if (isDev) {
    app.get('/panel', async (_, res) => {
      const { renderTrpcPanel } = await import('trpc-ui')

      return res
        .status(200)
        .header('Content-Type', 'text/html')
        .send(
          renderTrpcPanel(appRouter, {
            url: '/api/trpc',
            transformer: 'superjson',
            meta: {
              title: 'trpc-server',
              description: 'trpc-server',
            },
          }),
        )
    })
  }
}

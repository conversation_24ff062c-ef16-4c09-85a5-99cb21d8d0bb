import { z } from 'zod'
import { PublishService } from '@/lib/business/publish'
import { TRPCError } from '@trpc/server'
import { GetPublishRecordDetailSchema, PaginationSchema } from '@coozf/zod'
import { applicationProcedure } from '@/procedure'
import { logger } from '@/lib'

export const publishRouter = {
  // 获取发布记录列表
  list: applicationProcedure.input(PaginationSchema).query(async ({ input, ctx }) => {
    try {
      return PublishService.getPublishRecords({ ...input })
    } catch (error) {
      logger.error('获取发布记录列表失败', {
        type: 'router',
        operation: 'get_publish_records',
        error: error instanceof Error ? error.message : error,
      })

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '获取发布记录列表失败',
      })
    }
  }),

  // 获取发布记录详情
  detail: applicationProcedure.input(GetPublishRecordDetailSchema).query(async ({ input, ctx }) => {
    const { publishId } = input
    const { application } = ctx

    try {
      const record = await PublishService.getPublishRecordDetail(publishId)

      // 验证记录是否属于当前应用
      if (record.applicationId !== application.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '无权限访问该发布记录',
        })
      }

      return record
    } catch (error) {

      if (error instanceof TRPCError) {
        throw error
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '获取发布记录详情失败',
      })
    }
  }),

  // 获取发布统计
  stats: applicationProcedure.query(async ({ ctx }) => {
    const { application } = ctx

    try {
      const stats = await PublishService.getPublishStats(application.id)

      return {
        success: true,
        data: stats,
      }
    } catch (error) {
      logger.error('获取发布统计失败', {
        type: 'router',
        operation: 'get_publish_stats',
        error: error instanceof Error ? error.message : error,
      })

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '获取发布统计失败',
      })
    }
  }),
}

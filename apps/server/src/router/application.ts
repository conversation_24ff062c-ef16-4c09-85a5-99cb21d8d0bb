import { z } from 'zod'
import { router } from '@/trpc'
import type { Prisma } from '@coozf/db'
import { CreateApplicationSchema, UpdateApplicationSchema, ApplicationListSchema } from '@coozf/zod'
import { ApplicationService } from '@/lib'
import { applicationProcedure, applicationWithQuotaProcedure, protectedProcedure } from '@/procedure'
import { randomBytes } from 'crypto'
import { applicationCache } from '@/lib/services/cache'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: protectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    return ApplicationService.getApplicationList({
      ...input,
      userId: ctx.user.id,
      isUser: false,
    })
  }),

  // 创建应用
  create: protectedProcedure.input(CreateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 生成唯一的应用ID和密钥
    const appId = await ApplicationService.generateUniqueAppId()
    const { secret, hashedSecret } = await ApplicationService.generateAndHashSecret()
    const webhookSecret = `whsec_${randomBytes(16).toString('hex')}`

    const newApplication = await ctx.db.application.create({
      data: {
        ...input,
        appId,
        secret: hashedSecret, // 存储加密后的 Secret
        webhookSecret, // 存储 webhook 密钥
        userId: ctx.user.id,
      },
    })

    // 返回应用信息，包含明文 Secret（仅此一次）
    return {
      ...newApplication,
      secret, // 明文 Secret，仅在创建时返回
    }
  }),

  // 获取单个应用详情
  byId: applicationWithQuotaProcedure.query(async ({ ctx }) => {
    return ctx.applicationWithQuota
  }),

  // 更新应用
  update: applicationProcedure.input(UpdateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 只更新允许的字段
    const updateData: Prisma.ApplicationUpdateInput = {}
    if (input.name) updateData.name = input.name
    if (input.description !== undefined) updateData.description = input.description
    if (input.webhookUrl !== undefined) updateData.webhookUrl = input.webhookUrl
    if (input.status) updateData.status = input.status

    const updated = await ctx.db.application.update({
      where: { id: ctx.applicationId },
      data: updateData,
    })

    await applicationCache.del(ctx.applicationId)

    return updated
  }),

  // 删除应用
  delete: applicationProcedure.mutation(async ({ ctx }) => {
    await ctx.db.application.delete({
      where: { id: ctx.applicationId },
    })

    return { success: true }
  }),

  // 重新生成 Secret
  regenerateSecret: applicationProcedure.mutation(async ({ ctx }) => {
    // 生成新的 Secret
    const { secret, hashedSecret } = await ApplicationService.generateAndHashSecret()

    // 更新数据库中的 Secret
    const updated = await ctx.db.application.update({
      where: { id: ctx.applicationId },
      data: { secret: hashedSecret },
    })

    // 返回新的明文 Secret（仅此一次）
    return {
      ...updated,
      secret, // 明文 Secret，仅在重新生成时返回
      message: '密钥已重新生成，请妥善保存，此密钥不会再次显示',
    }
  }),

  // 获取应用趋势数据
  getApplicationTrends: applicationProcedure
    .input(
      z.object({
        days: z.number().min(1).max(90).default(30),
      }),
    )
    .query(async ({ ctx, input }) => {
      const daysAgo = new Date()
      daysAgo.setDate(daysAgo.getDate() - input.days)

      // 获取指定时间范围内的API调用记录
      const apiCalls = await ctx.db.apiCall.findMany({
        where: {
          applicationId: ctx.applicationId,
          createdAt: {
            gte: daysAgo,
          },
        },
      })

      // 过滤指定时间范围内的记录
      const filteredCalls = apiCalls.filter((call) => new Date(call.createdAt) >= daysAgo)

      // 按日期分组统计
      const dailyStatsMap = new Map<
        string,
        {
          apiCalls: number
          accountCount: number
          trafficGB: number
        }
      >()

      filteredCalls.forEach((call) => {
        const dateStr = new Date(call.createdAt).toISOString().split('T')[0] ?? ''
        const existing = dailyStatsMap.get(dateStr) ?? { apiCalls: 0, accountCount: 0, trafficGB: 0 }

        existing.apiCalls += 1
        if (call.costType === 'ACCOUNT_QUOTA') {
          existing.accountCount += 1
        }
        if (call.costType === 'TRAFFIC') {
          existing.trafficGB += Number(call.costAmount)
        }

        dailyStatsMap.set(dateStr, existing)
      })

      // 填充没有数据的日期
      const trends: {
        date: string
        apiCalls: number
        accountCount: number
        trafficGB: number
      }[] = []

      for (let i = input.days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0] ?? ''

        const existing = dailyStatsMap.get(dateStr)
        trends.push({
          date: dateStr,
          apiCalls: existing?.apiCalls ?? 0,
          accountCount: existing?.accountCount ?? 0,
          trafficGB: existing?.trafficGB ?? 0,
        })
      }

      return trends
    }),
})

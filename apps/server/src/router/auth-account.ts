import { z } from 'zod'
import { router } from '@/trpc'
import { applicationProcedure } from '@/procedure'
import { AccountListQuerySchema } from '@coozf/zod'
import { MediaAccountServies } from '@/lib'

export const authAccountRouter = router({
  // 获取授权账号列表
  list: applicationProcedure.input(AccountListQuerySchema).query(async ({ input, ctx }) => {
    return MediaAccountServies.getMediaAccountOrders({ ...input, applicationId: ctx.applicationId })
  }),

  // 删除账号
  delete: applicationProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.authAccount.delete({
      where: { id: input.id, applicationId: ctx.applicationId },
    })

    return { success: true, message: '账号删除成功' }
  }),
})

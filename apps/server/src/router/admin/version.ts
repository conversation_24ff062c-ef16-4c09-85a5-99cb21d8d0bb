import { z } from 'zod'
import { router } from '@/trpc'
import { VersionService } from '@/lib/business/version'
import { adminProtectedProcedure } from '@/procedure'
import { publicProcedure } from '@/trpc'
import { CheckUpdateSchema, CreateVersionSchema, UpdateVersionSchema, VersionListSchema } from '@coozf/zod'

export const versionRouter = router({
  // 创建新版本（需要管理员权限）
  create: adminProtectedProcedure.input(CreateVersionSchema).mutation(async ({ ctx, input }) => {
    return await VersionService.createVersion(input, ctx.user.id)
  }),

  // 获取版本列表（需要管理员权限）
  list: adminProtectedProcedure.input(VersionListSchema).query(async ({ input }) => {
    return await VersionService.getVersionList(input)
  }),

  // 更新版本信息（需要管理员权限）
  update: adminProtectedProcedure.input(UpdateVersionSchema).mutation(async ({ input }) => {
    const { id, ...updateData } = input
    return await VersionService.updateVersion(id, updateData)
  }),

  // 删除版本（软删除，需要管理员权限）
  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ input }) => {
    return await VersionService.deleteVersion(input.id)
  }),

  // 检查更新（公开接口）
  checkUpdate: publicProcedure.input(CheckUpdateSchema).query(async ({ input }) => {
    return await VersionService.checkForUpdate(input)
  }),
})

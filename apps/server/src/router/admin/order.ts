import { z } from 'zod'
import { router } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'
import { OrderService } from '@/lib'
import { CreateOrderSchema, OrderListSchema, PaginationSchema } from '@coozf/zod'

export const orderRouter = router({
  // 注意：adminRecharge 已废弃，请使用 admin/quota 路由中的配额管理功能

  // 获取订单列表
  list: adminProtectedProcedure.input(OrderListSchema.and(PaginationSchema)).query(async ({ input }) => {
    return await OrderService.getOrderList(input)
  }),

  // 获取订单详情
  getOrderById: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .query(async ({ input }) => {
      return OrderService.getOrderById(input.orderId)
    }),

  // 更新订单状态
  updateOrderStatus: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
        status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']),
        remarks: z.string().max(500, '备注不能超过500个字符').optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { orderId, status, remarks } = input
      
      // 获取原订单状态
      const originalOrder = await OrderService.getOrderById(orderId)
      if (!originalOrder) {
        throw new Error('订单不存在')
      }
      
      // 更新订单状态
      await OrderService.updateOrderStatus(orderId, status, remarks)
      
      // 如果订单状态从非完成状态变为完成状态，处理配额分配
      if (originalOrder.status !== 'COMPLETED' && status === 'COMPLETED') {
        await OrderService.processOrderCompletion(orderId)
      }
      
      return { success: true, message: '订单状态更新成功' }
    }),

  // 取消订单
  cancelOrder: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input }) => {
      await OrderService.cancelOrder(input.orderId)
      return { success: true, message: '订单已取消' }
    }),

  // 对公转账
  bankTransfer: adminProtectedProcedure
    .input(
      z.object({
        orderId: z.string().cuid('无效的订单ID'),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 获取原订单状态
      const originalOrder = await OrderService.getOrderById(input.orderId)
      if (!originalOrder) {
        throw new Error('订单不存在')
      }
      
      if (originalOrder.status === 'COMPLETED') {
        throw new Error('订单已经完成，无需重复操作')
      }
      
      if (originalOrder.status === 'CANCELLED') {
        throw new Error('已取消的订单无法开通转账')
      }
      
      // 使用事务处理整个流程
      await ctx.db.$transaction(async (tx) => {
        // 更新订单状态和支付方式
        await tx.order.update({
          where: { id: input.orderId },
          data: { 
            status: 'COMPLETED',
            paymentMethod: 'BANK_TRANSFER',
            remarks: '对公转账开通'
          },
        })
      })
      
      // 处理配额分配
      await OrderService.processOrderCompletion(input.orderId)
      
      return { success: true, message: '对公转账开通成功，订单已完成' }
    }),

  // 创建订单
  create: adminProtectedProcedure.input(CreateOrderSchema).mutation(async ({ input, ctx }) => {
    const orderNo = OrderService.generateOrderNo()
    
    // 根据订单类型设置初始状态
    const initialStatus = input.type === 'GIFT' ? 'COMPLETED' : 'PENDING'
    
    const order = await ctx.db.order.create({ 
      data: { 
        orderNo, 
        ...input,
        status: initialStatus
      } 
    })
    
    // 如果是赠送订单，立即处理配额分配
    if (input.type === 'GIFT') {
      await OrderService.processOrderCompletion(order.id)
    }
    
    return { success: true, orderNo, message: '订单创建成功' }
  }),
})

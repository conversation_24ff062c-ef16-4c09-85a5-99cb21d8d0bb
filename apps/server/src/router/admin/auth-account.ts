import { z } from 'zod'
import { router } from '@/trpc'
import { adminProtectedProcedure } from '@/procedure'
import { AccountListQuerySchema } from '@coozf/zod'
import { MediaAccountServies } from '@/lib'

export const authAccountRouter = router({
  // 获取授权账号列表
  list: adminProtectedProcedure.input(AccountListQuerySchema).query(async ({ input }) => {
    return MediaAccountServies.getMediaAccountOrders(input)
  }),

  // 获取账号详情
  getById: adminProtectedProcedure.input(z.object({ id: z.string() })).query(async ({ ctx, input }) => {
    const authAccount = await ctx.db.authAccount.findUnique({
      where: { id: input.id },
      include: {
        application: {
          select: {
            name: true,
            appId: true,
            userId: true,
          },
        },
      },
    })

    if (!authAccount) {
      throw new Error('授权账号不存在')
    }

    return authAccount
  }),

  // 删除账号
  delete: adminProtectedProcedure.input(z.object({ id: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.authAccount.delete({
      where: { id: input.id },
    })

    return { success: true, message: '账号删除成功' }
  }),

  // 批量删除账号
  batchDelete: adminProtectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.authAccount.deleteMany({
        where: {
          id: {
            in: input.ids,
          },
        },
      })

      return { success: true, message: `已删除 ${input.ids.length} 个账号` }
    }),
})

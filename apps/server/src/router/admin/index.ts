import { authRouter } from '@/router/admin/auth'
import { router, t } from '@/trpc'
import { orderRouter } from './order'
import { userRouter } from './user'
import { applicationRouter } from './application'
import { authAccountRouter } from './auth-account'
import { versionRouter } from './version'
import { ossRouter } from './oss'
import { adminUserRouter } from './admin-user'

export const appRouter = router({
  auth: authRouter,
  order: orderRouter,
  user: userRouter,
  application: applicationRouter,
  authAccount: authAccountRouter,
  version: versionRouter,
  oss: ossRouter,
  adminUser: adminUserRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter

export const createCaller = t.createCallerFactory(appRouter)

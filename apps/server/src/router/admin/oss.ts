import { adminOssService } from '@/lib/services/oss'
import { adminProtectedProcedure } from '@/procedure'
import { router } from '@/trpc'
import z from 'zod'

export const ossRouter = router({
  // 获取直传url
  getUploadSignatureUrl: adminProtectedProcedure
    .input(
      z.object({
        name: z.string(),
        version: z.string(),
        type: z.string(),
      }),
    )
    .query(async ({ input }) => {
      return await adminOssService.getUploadSignatureUrl(`${input.type}/${input.version}/${input.name}`)
    }),
})

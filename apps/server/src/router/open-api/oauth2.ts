import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import { db } from '@coozf/db'
import { OAuthService } from '@/lib'

// OAuth 授权 Schema
const OAuthAuthorizeSchema = z.object({
  platform: z.enum(['xiaohongshu', 'douyin', 'kuaishou']),
  scope: z.string().optional(),
})

export const oauth2Router = router({
  // 生成OAuth授权URL
  generateAuthorizeUrl: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/oauth2/authorize',
        summary: '生成OAuth授权URL',
        description: '为指定平台生成OAuth授权URL，用户点击后跳转到对应平台授权页面',
        tags: ['OAuth2'],
      },
    })
    .input(OAuthAuthorizeSchema)
    .output(
      z.object({
        authorizeUrl: z.string().url('授权URL'),
        state: z.string().describe('状态参数'),
        platform: z.string().describe('平台标识'),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { platform } = input

      if (!ctx.application.webhookUrl) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '应用未配置Webhook URL，请先在应用设置中配置',
        })
      }

      // 生成回调URL（指向我们的服务器）
      const redirectUri = `https://evidently-content-escargot.ngrok-free.app/api/oauth2/${platform}/callback`

      // 生成带有应用和平台信息的状态参数
      const state = OAuthService.generateOAuthState(ctx.application.id, platform)

      // 生成授权URL
      const authorizeUrl = OAuthService.generateAuthorizeUrl(platform, redirectUri, state)

      return {
        authorizeUrl,
        state,
        platform,
      }
    }),
})

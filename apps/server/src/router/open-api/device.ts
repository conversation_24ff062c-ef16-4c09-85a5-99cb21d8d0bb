import { z } from 'zod'
import { router } from '../../trpc'
import { deviceManager } from '../../lib/device-manager'
import { openAPIProcedure } from '@/procedure'

export const deviceRouter = router({

  // 查询在线设备
  // GET /devices/online - 查询设备是否在线
  filterOnline: openAPIProcedure
    .input(z.object({
      deviceIds: z.string().transform((str) => {
        // 支持逗号分隔的字符串转换为数组
        return str.split(',').map(id => id.trim()).filter(id => id.length > 0)
      })
    }))
    .meta({
      openapi: {
        method: 'GET',
        path: '/devices/online',
        protect: true,
        tags: ['在线设备'],
        summary: '查询设备是否在线',
        description: '通过设备ID列表查询哪些设备当前在线。设备ID使用逗号分隔，例如：device1,device2,device3'
      },
    })
    .output(z.array(z.string()))
    .query(async ({ input }) => {
      return await deviceManager.filterOnline(input.deviceIds)
    }),
})
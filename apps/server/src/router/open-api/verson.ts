import { VersionService } from '@/lib'
import { publicProcedure, router } from '@/trpc'
import { CheckUpdateResSchema, CheckUpdateSchema } from '@coozf/zod'

export const versonRouter = router({
  // 检测版本更新
  checkUpdate: publicProcedure
    .input(CheckUpdateSchema)
    .meta({
      openapi: {
        method: 'GET',
        path: '/verson/checkForUpdate',
        tags: ['版本'],
        summary: '检测版本更新',
      },
    })
    .output(CheckUpdateResSchema)
    .query(async ({ input }) => {
      return await VersionService.checkForUpdate(input)
    }),
})

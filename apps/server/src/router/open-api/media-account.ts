import { z } from 'zod'
import { router } from '@/trpc'
import { openAPIProcedure, verifySecretKey } from '@/procedure'
import {
  AccountListQuerySchema,
  BindAccountSchema,
  GetAccountByIdSchema,
  BindAccountResponseSchema,
  MediaAccountResponseSchema,
  createPaginationResponseSchema,
  UpdateAccountSchema,
} from '@coozf/zod'
import type { Prisma } from '@coozf/db'
import { TRPCError } from '@trpc/server'
import { MediaAccountServies, QuotaService } from '@/lib'
import { CookieService } from '@/lib/services/cookie'
import { accountCache } from '@/lib/services/cache'

export const requestHeader = z.object({
  'x-secret-key': z.string().describe('加密密钥，用于加密Cookie'),
})

export const openMediaAccountRouter = router({
  // POST /media-accounts - 创建(绑定)媒体账号
  create: openAPIProcedure
    .input(BindAccountSchema)
    .meta({
      openapi: {
        method: 'POST',
        path: '/media-accounts',
        protect: true,
        requestHeaders: requestHeader,
        tags: ['媒体账号'],
        summary: '绑定媒体账号',
        description: '将指定平台的账号绑定到当前应用。用户传入原始Cookie，后端自动加密处理。',
      },
    })
    .output(BindAccountResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const { platformCode, platformUserId, platformUserName, platformAvatar, platformCookie } = input

      const secretKey = verifySecretKey(ctx.req)

      // 验证账号额度
      if (!(await QuotaService.hasSufficientAccountQuota(ctx.application.id, 1))) {
        throw new TRPCError({
          code: 'PAYMENT_REQUIRED',
          message: '账号配额不足，无法绑定媒体账号。',
        })
      }

      // // 检查平台账号是否已绑定
      // const existingAccount = await ctx.db.authAccount.findFirst({
      //   where: {
      //     applicationId: ctx.application.id,
      //     platformCode: platformCode,
      //     platformUserId,
      //   },
      // })

      // if (existingAccount) {
      //   throw new TRPCError({
      //     code: 'CONFLICT',
      //     message: '该平台账号已绑定到当前应用，请勿重复绑定。',
      //   })
      // }

      // 对Cookie进行加密处理
      const hashedCookie = await CookieService.encrypt(platformCookie, secretKey)

      // 创建媒体账号绑定
      const authAccount = await ctx.db.authAccount.create({
        data: {
          applicationId: ctx.application.id,
          platformCode,
          platformUserId,
          platformUserName,
          platformAvatar,
          platformCookieHash: hashedCookie,
        },
      })

      return {
        id: authAccount.id,
      }
    }),

  // GET /media-accounts - 获取媒体账号列表
  list: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/media-accounts',
        protect: true,
        tags: ['媒体账号'],
        summary: '获取媒体账号列表',
        description: '获取当前应用下绑定的所有媒体账号列表，支持分页和筛选。',
      },
    })
    .input(AccountListQuerySchema)
    .output(createPaginationResponseSchema(MediaAccountResponseSchema))
    .query(async ({ ctx, input }) => {
      return MediaAccountServies.getMediaAccountOrders({
        ...input,
        applicationId: ctx.application.id,
      })
    }),

  // GET /media-accounts/{id} - 获取单个媒体账号详情
  getById: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/media-accounts/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '获取媒体账号详情',
        description: '根据ID获取指定媒体账号的详细信息。',
      },
    })
    .input(GetAccountByIdSchema)
    .output(MediaAccountResponseSchema)
    .query(async ({ ctx, input }) => {
      const authAccount = await ctx.db.authAccount.findFirst({
        where: {
          id: input.id,
          applicationId: ctx.application.id,
        },
      })

      if (!authAccount) {
        throw new Error('媒体账号不存在或无权限访问')
      }

      return {
        id: authAccount.id,
        applicationId: authAccount.applicationId,
        platformCode: authAccount.platformCode as any,
        platformUserId: authAccount.platformUserId,
        platformUserName: authAccount.platformUserName,
        platformAvatar: authAccount.platformAvatar,
        createdAt: authAccount.createdAt,
        updatedAt: authAccount.updatedAt,
      }
    }),

  // DELETE /media-accounts/{id} - 删除媒体账号
  delete: openAPIProcedure
    .meta({
      openapi: {
        method: 'DELETE',
        path: '/media-accounts/{id}',
        protect: true,
        tags: ['媒体账号'],
        summary: '删除媒体账号',
        description: '删除指定的媒体账号绑定，解除与当前应用的关联。',
      },
    })
    .input(GetAccountByIdSchema)
    .output(z.object({}))
    .mutation(async ({ ctx, input }) => {
      // 验证账号是否属于当前应用
      const existingAccount = await MediaAccountServies.getFindFirst({
        id: input.id,
        applicationId: ctx.application.id,
      })

      if (!existingAccount) {
        throw new Error('媒体账号不存在或无权限访问')
      }

      // 删除账号
      await ctx.db.authAccount.delete({
        where: { id: existingAccount.id },
      })

      await accountCache.del(existingAccount.id)

      return {}
    }),

  // put /media-accounts/{id} - 更新媒体账号
  upadate: openAPIProcedure
    .meta({
      openapi: {
        method: 'PUT',
        path: '/media-accounts/{id}',
        protect: true,
        requestHeaders: requestHeader,
        tags: ['媒体账号'],
        summary: '更新媒体账号',
        description: '将指定平台的账号绑定到当前应用。用户传入原始Cookie，后端自动进行安全哈希处理。',
      },
    })
    .input(UpdateAccountSchema)
    .output(BindAccountResponseSchema)
    .mutation(async ({ ctx, input }) => {
      const { platformCode, platformUserId, platformUserName, platformAvatar, platformCookie } = input

      const secretKey = verifySecretKey(ctx.req)

      // 检查平台账号是否已绑定
      const existingAccount = await MediaAccountServies.getFindFirst({
        id: input.id,
        applicationId: ctx.application.id,
        platformCode: platformCode,
        platformUserId,
      })

      if (!existingAccount) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: '该平台账号未绑定到当前应用，请先备案。',
        })
      }

      const data: Prisma.AuthAccountUpdateInput = {}

      // 对Cookie进行哈希处理
      if (platformCookie) {
        data.platformCookieHash = await CookieService.encrypt(platformCookie, secretKey)
      }
      if (platformUserName) {
        data.platformUserName = platformUserName
      }
      if (platformAvatar) {
        data.platformAvatar = platformAvatar
      }
      // 更新数据

      // 创建媒体账号绑定
      const authAccount = await ctx.db.authAccount.update({
        where: {
          id: existingAccount.id,
        },
        data,
      })
      await accountCache.del(existingAccount.id)

      return {
        id: authAccount.id,
      }
    }),
})

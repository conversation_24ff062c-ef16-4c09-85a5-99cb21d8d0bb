import type { FastifyInstance } from 'fastify'
import { WebhookService, OAuthService, logger } from '@/lib'
import { ResponseWrapper, ApiError } from '@/lib'
import { db } from '@coozf/db'

export async function oauth2Routes(app: FastifyInstance) {
  // 通用OAuth2回调处理
  app.get('/:platform/callback', async (request, reply) => {
    const { platform } = request.params as { platform: string }
    const { auth_code: code, state } = request.query as { auth_code?: string; state?: string }

    logger.info(`处理OAuth2回调`, {
      type: 'oauth2',
      platform,
      requestId: request.id,
      hasCode: !!code,
      hasState: !!state,
      codePrefix: code?.substring(0, 10),
    })

    // 参数验证
    if (!code || !state) {
      throw new ApiError(40001, '缺少必要参数 code 或 state')
    }

    try {
      // 解析状态参数
      const stateInfo = OAuthService.parseOAuthState(state)
      if (!stateInfo) {
        throw new ApiError(40002, '无效的状态参数')
      }

      const { appId, platform: statePlatform } = stateInfo

      // 验证平台匹配
      if (platform !== statePlatform) {
        throw new ApiError(40003, '平台不匹配')
      }

      const application = await db.application.findUnique({ where: { id: appId } })

      if (!application) {
        throw new ApiError(40401, '应用不存在')
      }

      // 通过授权码获取访问令牌
      logger.info(`获取访问令牌`, {
        type: 'oauth2',
        platform,
        appId,
        requestId: request.id,
      })
      const tokenResult = await OAuthService.getAccessToken(platform, code)

      if (!tokenResult.success || !tokenResult.data) {
        logger.error(`OAuth2获取访问令牌失败`, {
          type: 'oauth2',
          platform,
          appId,
          requestId: request.id,
          error: tokenResult.error,
        })
        throw new ApiError(40004, tokenResult.error || '获取访问令牌失败')
      }

      logger.info(`OAuth2获取访问令牌成功`, {
        type: 'oauth2',
        platform,
        appId,
        requestId: request.id,
        tokenResult,
      })

      const { user_id, name } = tokenResult.data

      // 检查是否已存在该用户的授权记录
      const existingAccount = await db.authAccount.findFirst({
        where: {
          platformCode: platform,
          applicationId: appId,
          platformUserId: user_id,
        },
        select: {
          id: true,
          applicationId: true,
          platformCode: true,
          platformUserId: true,
          isDeleted: true
        },
      })


      // 计算token过期时间：当前时间 + expires_in秒数
      const tokenExpiresAt = new Date(Date.now() + tokenResult.data.expires_in * 1000)
      const tokenJsonStr = JSON.stringify(tokenResult.data)

      // 更新或创建授权记录
      if (existingAccount) {
        // 更新现有记录
        await db.authAccount.update({
          where: { id: existingAccount.id },
          data: {
            platformCookieHash: tokenJsonStr,
            platformTokenExpiresAt: tokenExpiresAt,
            // scope: scope,
            state: null, // 清除状态
          },
        })
      } else {
        // 创建新记录
        await db.authAccount.create({
          data: {
            platformCode: platform,
            platformUserId: user_id,
            platformCookieHash: tokenJsonStr,
            authType: 'TOKEN',
            platformTokenExpiresAt: tokenExpiresAt,
            platformUserName: '',
            platformAvatar: '',
            // scope: scope,
            state: null, // 授权完成后清除状态
            applicationId: appId,
          },
        })
      }

      // 发送Webhook通知
      logger.info(`发送OAuth2 Webhook通知`, {
        type: 'oauth2',
        platform,
        appId,
        applicationName: application.name,
        webhookUrl: application.webhookUrl,
        requestId: request.id,
      })
      const webhookSuccess = await WebhookService.sendNotification(
        application.webhookUrl || '',
        application.webhookSecret || '',
        statePlatform,
        tokenResult,
        'oauth2',
      )

      if (!webhookSuccess) {
        logger.warn(`OAuth2 Webhook通知发送失败`, {
          type: 'oauth2',
          platform,
          appId,
          applicationName: application.name,
          requestId: request.id,
        })
      }

      // 返回成功页面
      return reply.type('text/html').send(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${platform} 授权成功</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #4CAF50; }
            .info { color: #2196F3; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1 class="success">✅ ${platform} 授权成功</h1>
          <p class="info">您已成功完成 ${platform} 账号授权</p>
          <p>用户: ${name || user_id}</p>
          <p>您可以关闭此页面</p>
          <script>
            // 3秒后自动关闭窗口
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
        </html>
      `)
    } catch (error) {
      logger.error(`OAuth2回调处理失败`, {
        type: 'oauth2',
        platform,
        requestId: request.id,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
      })
      if (error instanceof ApiError) throw error
      throw new ApiError(50000, `授权处理失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  })

  // webhook
  app.post('/webhook', async (request) => {
    logger.info('OAuth2 Webhook接收', {
      type: 'oauth2',
      requestId: request.id,
      body: request.body,
    })
    return ResponseWrapper.success('Webhook received')
  })
}

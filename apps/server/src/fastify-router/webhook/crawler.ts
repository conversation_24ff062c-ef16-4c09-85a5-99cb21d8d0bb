import { db } from '@coozf/db'
import type { Application } from '@coozf/db'
import { WebhookService, sessionService, ApiError, PublishService, logger } from '@/lib'
import type { FastifyInstance } from 'fastify'
import { CrawlerWebhookSchema, PublishTaskCallbackSchema } from '@coozf/zod'
import { applicationCache } from '@/lib/services/cache'

declare module 'fastify' {
  interface FastifyRequest {
    application: Pick<Application, 'id' | 'name' | 'appId' | 'webhookUrl' | 'webhookSecret'>
    recordId: string
  }
}

export const crawlerWebhookRoutes = async (app: FastifyInstance) => {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const sessionToken = await sessionService.getSessionToken(authHeader ?? '')
    request.recordId = sessionToken.recordId
    // 获取应用信息
    const existingApp = await applicationCache.getOrSetDefault(sessionToken.applicationId)
    if (!existingApp) {
      throw new ApiError(40100, 'Unauthorized') /*  */
    }
    request.application = existingApp
  })
  app.post('/', async (request, reply) => {
    const { application, body } = request
    const res = CrawlerWebhookSchema.parse(body)
    logger.info('收到爬虫Webhook请求', {
      type: 'webhook',
      source: 'crawler',
      applicationId: application.id,
      recordId: request.recordId,
      body,
    })
    if (request.recordId && res.event === 'publishResult') {
      const publishResult = PublishTaskCallbackSchema.parse(body)
      logger.info('收到发布结果回调', {
        type: 'webhook',
        source: 'crawler',
        event: 'publishResult',
        applicationId: application.id,
        recordId: request.recordId,
        publishResult,
      })

      if (publishResult.stages === 'push') {
        const result = await PublishService.handleTaskCallback(
          publishResult.taskId,
          request.recordId,
          publishResult.stageStatus === 'success',
          publishResult.errorMessage,
        )
        logger.info('处理发布结果回调完成', {
          type: 'webhook',
          source: 'crawler',
          event: 'publishResult_processed',
          applicationId: application.id,
          recordId: request.recordId,
          result,
          publishResult,
        })
      }
    }

    const { webhookUrl, webhookSecret } = application
    if (!webhookUrl) {
      return reply.status(400).send({
        success: false,
        message: 'Webhook URL not found',
      })
    } else {
      await WebhookService.sendNotification(webhookUrl, webhookSecret, 'web', request.body)
    }
    return reply.send({
      statusCode: 0,
      message: 'Webhook received',
    })
  })
}

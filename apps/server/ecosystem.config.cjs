module.exports = {
  apps: [
    {
      name: 'coozf-server',
      script: 'dist/index.js',
      autorestart: true,
      watch: false,
      // 开发环境配置
      env: {
        MODE: 'dev',
        NODE_ENV: 'development',
      },

      // 生产环境配置
      env_prod: {
        MODE: 'prod',
        NODE_ENV: 'production',
      },

      // 测试环境配置
      env_pre: {
        MODE: 'pre',
        NODE_ENV: 'development',
      },
    },
    {
      name: 'coozf-admin',
      script: 'dist/admin.js',
      instances: 1,
      autorestart: true,
      watch: false,

      // 开发环境配置
      env: {
        MODE: 'dev',
        NODE_ENV: 'development',
      },

      // 生产环境配置
      env_prod: {
        MODE: 'prod',
        NODE_ENV: 'production',
      },

      // 测试环境配置
      env_pre: {
        MODE: 'pre',
        NODE_ENV: 'development',
      },
    },
  ],
}

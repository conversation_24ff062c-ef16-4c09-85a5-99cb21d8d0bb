import { <PERSON><PERSON><PERSON>r, Chart<PERSON>ooltip, ChartTooltipContent } from '@coozf/ui/components/chart'
import { LineChart, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts'
import type { TrendData } from '../types'
import { CHART_CONFIG } from '../constants'

interface TrendsChartProps {
  data: TrendData[]
}

export function TrendsChart({ data }: TrendsChartProps) {
  if (!data || data.length === 0) {
    return null
  }

  return (
    <div>
      <h3 className="text-lg font-medium mb-4">30日数据趋势</h3>
      <ChartContainer config={CHART_CONFIG}>
        <LineChart
          accessibilityLayer
          data={data}
          margin={{
            left: 12,
            right: 12,
          }}
        >
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="date"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => {
              const date = new Date(value)
              return `${date.getMonth() + 1}/${date.getDate()}`
            }}
          />
          <YAxis />
          <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
          <Line
            dataKey="accountCount"
            type="monotone"
            stroke="var(--color-accountCount)"
            strokeWidth={2}
            dot={false}
          />
          <Line
            dataKey="trafficGB"
            type="monotone"
            stroke="var(--color-trafficGB)"
            strokeWidth={2}
            dot={false}
          />
        </LineChart>
      </ChartContainer>
    </div>
  )
}
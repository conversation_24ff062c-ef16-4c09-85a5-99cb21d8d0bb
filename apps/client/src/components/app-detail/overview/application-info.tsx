import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Copy, Eye, EyeOff } from 'lucide-react'
import type { Application } from '../types'

interface ApplicationInfoProps {
  application: Application
  onCopyToClipboard: (text: string) => void
}

export function ApplicationInfo({ application, onCopyToClipboard }: ApplicationInfoProps) {
  const [showSecret, setShowSecret] = useState(false)

  return (
    <Card>
      <CardHeader>
        <CardTitle>概览</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-muted-foreground w-30">应用名称</label>
            <div className="font-medium">{application.name}</div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-muted-foreground w-30">应用ID</label>
            <div className="flex items-center gap-2">
              <code className="text-sm bg-muted px-2 py-1 rounded border font-mono">{application.appId}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onCopyToClipboard(application.appId)}
                className="h-7 w-7 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-muted-foreground w-30">Secret</label>
            <div className="">已加密存储</div>
          </div>

          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-muted-foreground w-30">Webhook Secret</label>
            <div className="flex items-center gap-2">
              <code className="text-sm bg-muted px-2 py-1 rounded border font-mono">
                {showSecret
                  ? application.webhookSecret || '••••••••••••••••••••••••••••••••'
                  : '••••••••••••••••••••••••••••••••'}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSecret(!showSecret)}
                className="h-7 w-7 p-0"
              >
                {showSecret ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
              </Button>
              {showSecret && application.webhookSecret && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onCopyToClipboard(application.webhookSecret)}
                  className="h-7 w-7 p-0"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
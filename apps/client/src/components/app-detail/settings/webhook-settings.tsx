import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import type { Application } from '../types'
import { useWebhookSettings } from '../hooks/useWebhookSettings'

interface WebhookSettingsProps {
  applicationId: string
  application: Application
}

export function WebhookSettings({ applicationId, application }: WebhookSettingsProps) {
  const { isEditingWebhook, webhookForm, onWebhookSubmit, handleEditWebhook, handleCancelWebhook } = useWebhookSettings(
    applicationId,
    application.webhookUrl || undefined,
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Webhook</CardTitle>
        <CardDescription>配置接收事件通知的URL地址</CardDescription>
      </CardHeader>
      <CardContent>
        {!isEditingWebhook ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Webhook URL</Label>
              {application.webhookUrl ? (
                <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                  <code className="text-sm">{application.webhookUrl}</code>
                  <Button variant="outline" size="sm" onClick={handleEditWebhook}>
                    修改
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                  <span className="text-sm text-muted-foreground">未设置</span>
                  <Button variant="outline" size="sm" onClick={handleEditWebhook}>
                    设置
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <form onSubmit={webhookForm.handleSubmit(onWebhookSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="webhookUrl">Webhook URL</Label>
              <Input
                id="webhookUrl"
                {...webhookForm.register('url')}
                placeholder="https://your-domain.com/webhook"
                type="url"
              />
              {webhookForm.formState.errors.url && (
                <p className="text-sm text-destructive">{webhookForm.formState.errors.url.message}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Button type="submit">保存</Button>
              <Button type="button" variant="outline" onClick={handleCancelWebhook}>
                取消
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  )
}

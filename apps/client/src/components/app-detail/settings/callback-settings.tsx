import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@coozf/ui/components/alert-dialog'
import { useCallbackSettings } from '../hooks/useCallbackSettings'

interface CallbackSettingsProps {
  applicationId: string
}

export function CallbackSettings({ applicationId }: CallbackSettingsProps) {
  const {
    callbackUrl,
    isEditingCallback,
    setIsEditingCallback,
    showCallbackConfirm,
    callbackForm,
    onCallbackSubmit,
    confirmCallbackSave,
    cancelCallbackSave,
    handleCancelCallback,
  } = useCallbackSettings()

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>回调URL</CardTitle>
          <CardDescription>
            请填写用于接收用户授权成功的code和token的回调URL（包含域名+path）进行平台备案，若无备案将拦截回调，只可填写1次且不可修改，请谨慎填写
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isEditingCallback ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>回调URL</Label>
                {callbackUrl ? (
                  <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <code className="text-sm">{callbackUrl}</code>
                    <span className="text-sm text-muted-foreground">不可修改</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <span className="text-sm text-muted-foreground">未设置</span>
                    <Button variant="outline" size="sm" onClick={() => setIsEditingCallback(true)}>
                      设置
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <form onSubmit={callbackForm.handleSubmit(onCallbackSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="callbackUrl">回调URL</Label>
                <Input
                  id="callbackUrl"
                  {...callbackForm.register('url')}
                  placeholder="https://your-domain.com/callback"
                  type="url"
                />
                {callbackForm.formState.errors.url && (
                  <p className="text-sm text-destructive">{callbackForm.formState.errors.url.message}</p>
                )}
              </div>
              <div className="flex gap-2">
                <Button type="submit">保存</Button>
                <Button type="button" variant="outline" onClick={handleCancelCallback}>
                  取消
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>

      {/* 回调URL确认弹框 */}
      <AlertDialog open={showCallbackConfirm} onOpenChange={() => {}}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认保存回调URL</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelCallbackSave}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCallbackSave}>确认保存</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

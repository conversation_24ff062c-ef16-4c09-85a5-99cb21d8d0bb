import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useMutation } from '@tanstack/react-query'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import type { WebhookFormData } from '../types'

const webhookSchema = z.object({ 
  url: z.string().url('请输入有效的URL') 
})

export function useWebhookSettings(applicationId: string, initialWebhookUrl?: string) {
  const [isEditingWebhook, setIsEditingWebhook] = useState(false)

  const webhookForm = useForm<WebhookFormData>({
    resolver: zodResolver(webhookSchema),
    defaultValues: { url: '' },
  })

  const updateApplicationMutation = useMutation(
    trpc.application.update.mutationOptions({
      onSuccess: () => {
        toast.success('应用更新成功')
        setIsEditingWebhook(false)
        webhookForm.reset()
      },
      onError: (error) => {
        toast.error(error.message)
      },
    })
  )

  const onWebhookSubmit = (data: WebhookFormData) => {
    updateApplicationMutation.mutate({
      applicationId,
      webhookUrl: data.url,
    })
  }

  const handleEditWebhook = () => {
    setIsEditingWebhook(true)
    webhookForm.setValue('url', initialWebhookUrl || '')
  }

  const handleCancelWebhook = () => {
    setIsEditingWebhook(false)
    webhookForm.reset()
  }

  return {
    isEditingWebhook,
    setIsEditingWebhook,
    webhookForm,
    updateApplicationMutation,
    onWebhookSubmit,
    handleEditWebhook,
    handleCancelWebhook,
  }
}
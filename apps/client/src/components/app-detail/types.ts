import type { RouterOutput } from '@/lib/trpc'

// API类型定义
export type Application = RouterOutput['application']['byId']
export type TrendData = RouterOutput['application']['getApplicationTrends'][0]
export type ClientVersion = RouterOutput['clientVersion']['list']['data'][0]
export type BaseVersion = RouterOutput['clientVersion']['getLatestPublicVersion']

// 组件Props类型
export interface TabComponentProps {
  applicationId: string
  application: Application
}

// 表单数据类型
export interface WebhookFormData {
  url: string
}

export interface CallbackFormData {
  url: string
}

export interface CreateVersionFormData {
  baseVersionId: string
  version: string
  platform: 'WIN' | 'MAC'
  gitlabProjectId: string
}

// 菜单项类型
export interface MenuItem {
  id: 'overview' | 'settings' | 'client-management'
  label: string
  icon: any
}

import { SecretManagement } from '../settings/secret-management'
import { WebhookSettings } from '../settings/webhook-settings'
import { CallbackSettings } from '../settings/callback-settings'
import type { TabComponentProps } from '../types'

export function SettingsTab({ applicationId, application }: TabComponentProps) {
  return (
    <div className="space-y-6">
      <SecretManagement applicationId={applicationId} />
      <WebhookSettings applicationId={applicationId} application={application} />
      <CallbackSettings applicationId={applicationId} />
    </div>
  )
}
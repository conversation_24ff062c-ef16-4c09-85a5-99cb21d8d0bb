import { OEMConfiguration } from '../oem/oem-configuration'
import { ClientVersions } from '../oem/client-versions'
import { OEMConfigDialog } from '../oem/oem-configDialog'
import { CreateVersionDialog } from '../oem/create-versionDialog'
import { useOEMManagement } from '../hooks/useOEMManagement'
import type { TabComponentProps } from '../types'

export function ClientManagementTab({ applicationId, application }: TabComponentProps) {
  const {
    showOEMConfigDialog,
    setShowOEMConfigDialog,
    showCreateVersionDialog,
    setShowCreateVersionDialog,
    clientVersions,
    baseVersions,
    handleOEMConfigSave,
    handleCreateVersion,
  } = useOEMManagement(applicationId)

  // 从应用数据中获取OEM配置 (需要根据实际API响应结构调整)
  const oemConfig = (application as any)?.oemConfig
  const oemEnabled = (application as any)?.oemEnabled || false

  return (
    <>
      <div className="space-y-6">
        {/* OEM配置 */}
        {oemEnabled && <OEMConfiguration oemConfig={oemConfig} onConfigureClick={() => setShowOEMConfigDialog(true)} />}

        {/* 客户端版本管理 */}
        {oemEnabled && oemConfig && (
          <ClientVersions
            clientVersions={clientVersions?.data}
            onCreateVersion={() => setShowCreateVersionDialog(true)}
          />
        )}
      </div>

      {/* OEM配置对话框 */}
      <OEMConfigDialog
        open={showOEMConfigDialog}
        onOpenChange={setShowOEMConfigDialog}
        initialConfig={oemConfig}
        onSave={handleOEMConfigSave}
        applicationId={applicationId}
      />

      {/* 创建版本对话框 */}
      {baseVersions && (
        <CreateVersionDialog
          open={showCreateVersionDialog}
          onOpenChange={setShowCreateVersionDialog}
          applicationId={applicationId}
          baseVersions={baseVersions}
          onSave={handleCreateVersion}
        />
      )}
    </>
  )
}

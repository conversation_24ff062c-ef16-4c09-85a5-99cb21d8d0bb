import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Switch } from '@coozf/ui/components/switch'
import { Smartphone } from 'lucide-react'

interface OEMToggleProps {
  oemEnabled: boolean
  onToggle: (enabled: boolean) => void
}

export function OEMToggle({ oemEnabled, onToggle }: OEMToggleProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="w-5 h-5" />
          OEM客户端管理
        </CardTitle>
        <CardDescription>启用OEM功能后，您可以基于公用版本定制自己的品牌客户端</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="font-medium">OEM功能</div>
            <div className="text-sm text-muted-foreground">{oemEnabled ? '已启用' : '未启用'}</div>
          </div>
          <Switch checked={oemEnabled} onCheckedChange={onToggle} />
        </div>
      </CardContent>
    </Card>
  )
}
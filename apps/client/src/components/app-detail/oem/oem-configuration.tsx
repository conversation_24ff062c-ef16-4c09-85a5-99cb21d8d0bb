import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { But<PERSON> } from '@coozf/ui/components/button'
import { Label } from '@coozf/ui/components/label'
import { Settings } from 'lucide-react'
import type { OEMConfig } from '@coozf/zod'

interface OEMConfigurationProps {
  oemConfig?: OEMConfig
  onConfigureClick: () => void
}

export function OEMConfiguration({ oemConfig, onConfigureClick }: OEMConfigurationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>品牌定制配置</CardTitle>
        <CardDescription>设置您的品牌信息和客户端外观</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {!oemConfig ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-4">尚未配置品牌信息</div>
              <Button onClick={onConfigureClick}>
                <Settings className="w-4 h-4 mr-2" />
                配置品牌信息
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">应用名称</Label>
                  <div className="text-sm text-muted-foreground">{oemConfig.name || '未设置'}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">简短名称</Label>
                  <div className="text-sm text-muted-foreground">{oemConfig.shortName || '未设置'}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">包名</Label>
                  <div className="text-sm text-muted-foreground">{oemConfig.packageName || '未设置'}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Logo</Label>
                  <div className="text-sm text-muted-foreground">{oemConfig.logoUrl ? '已设置' : '未设置'}</div>
                </div>
              </div>
              <div className="flex gap-2 pt-4">
                <Button variant="outline" onClick={onConfigureClick}>
                  <Settings className="w-4 h-4 mr-2" />
                  修改配置
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

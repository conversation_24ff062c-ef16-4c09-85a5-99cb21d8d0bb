import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { But<PERSON> } from '@coozf/ui/components/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Plus, Play, Download } from 'lucide-react'
import { StatusBadge } from './status-badge'
import type { ClientVersion } from '../types'

interface ClientVersionsProps {
  clientVersions?: ClientVersion[]
  onCreateVersion: () => void
}

export function ClientVersions({ clientVersions, onCreateVersion }: ClientVersionsProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>客户端版本管理</CardTitle>
            <CardDescription>管理基于公用版本的定制客户端</CardDescription>
          </div>
          <Button onClick={onCreateVersion}>
            <Plus className="w-4 h-4 mr-2" />
            创建版本
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {!clientVersions?.length ? (
          <div className="text-center py-8">
            <div className="text-muted-foreground mb-4">暂无客户端版本</div>
            <Button variant="outline" onClick={onCreateVersion}>
              <Plus className="w-4 h-4 mr-2" />
              创建第一个版本
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>版本号</TableHead>
                <TableHead>平台</TableHead>
                <TableHead>构建状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {clientVersions.map((version) => (
                <TableRow key={version.id}>
                  <TableCell className="font-medium">{version.baseVersion.version}</TableCell>
                  <TableCell>{version.platform || '-'}</TableCell>
                  <TableCell>
                    <StatusBadge status={version.buildStatus} />
                  </TableCell>
                  <TableCell>{new Date(version.createdAt).toLocaleDateString('zh-CN')}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {version.buildStatus === 'PENDING' && (
                        <Button size="sm" variant="outline" onClick={() => onCreateVersion()}>
                          <Play className="w-3 h-3 mr-1" />
                          构建
                        </Button>
                      )}
                      {version.buildStatus === 'SUCCESS' && version.downloadUrl && (
                        <Button size="sm" variant="outline" asChild>
                          <a href={version.downloadUrl} target="_blank" rel="noopener noreferrer">
                            <Download className="w-3 h-3 mr-1" />
                            下载
                          </a>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

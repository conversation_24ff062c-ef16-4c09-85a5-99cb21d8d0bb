import { useState } from 'react'
import { TableCell, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import { Copy, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import { toNumber, formatAccountCount } from '@/lib/utils'
import { DeleteApplicationDialog } from './DeleteApplicationDialog'
import type { Application } from './ApplicationTable'

interface ApplicationRowProps {
  application: Application
  onRowClick: (id: string) => void
  onDelete: (id: string) => void
}

export function ApplicationRow({ application, onRowClick, onDelete }: ApplicationRowProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const handleDelete = () => {
    onDelete(application.id)
    setShowDeleteDialog(false)
  }

  return (
    <>
      <TableRow className="cursor-pointer hover:bg-muted/50" onClick={() => onRowClick(application.id)}>
        <TableCell>
          <div className="font-medium">{application.name}</div>
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <code className="text-sm bg-muted px-2 py-1 rounded">{application.appId}</code>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                copyToClipboard(application.appId)
              }}
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </TableCell>
        <TableCell>
          <Badge variant="secondary">
            {formatAccountCount(application.authAccountsCount, application.accountQuota)}
          </Badge>
        </TableCell>
        <TableCell>
          <Badge variant="outline">{application.trafficQuotaGB}GB</Badge>
        </TableCell>
        <TableCell>
          {application.accountQuotaExpireDate
            ? application.accountQuotaExpireDate.toLocaleDateString('zh-CN')
            : '无限期'}
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setShowDeleteDialog(true)
              }}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </TableCell>
      </TableRow>

      <DeleteApplicationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        applicationName={application.name}
        onConfirm={handleDelete}
      />
    </>
  )
}

import { Button } from '@coozf/ui/components/button'
import { Label } from '@coozf/ui/components/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@coozf/ui/components/dialog'
import { Copy, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

interface ApplicationSecretDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  applicationName: string
  secret: string
  onClose: () => void
}

export function ApplicationSecretDialog({
  open,
  onOpenChange,
  applicationName,
  secret,
  onClose,
}: ApplicationSecretDialogProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const handleClose = () => {
    onClose()
    toast.success('应用创建成功')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-amber-500" />
            重要：请保存您的密钥
          </DialogTitle>
          <DialogDescription>
            应用 "<strong>{applicationName}</strong>" 创建成功！请立即复制并保存以下密钥，此密钥将不会再次显示。
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>应用密钥 (Secret)</Label>
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md border">
              <code className="flex-1 text-sm font-mono break-all">
                {secret}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(secret)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-amber-500 mt-0.5" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">注意事项：</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>此密钥用于API调用认证，请妥善保管</li>
                  <li>密钥泄露可能导致安全风险</li>
                  <li>关闭此对话框后将无法再次查看</li>
                  <li>如需重新生成，请在应用详情页面操作</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleClose} className="w-full">
            我已保存密钥
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
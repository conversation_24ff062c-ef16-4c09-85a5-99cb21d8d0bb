import { Card, CardContent } from '@coozf/ui/components/card'
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { ApplicationRow } from './ApplicationRow'
import { Pagination } from './Pagination'
import type { RouterOutput } from '@/lib/trpc'

export type Application = RouterOutput['application']['list']['items'][number]

interface ApplicationTableProps {
  applications: Application[]
  currentPage: number
  totalPages: number
  totalItems: number
  onPageChange: (page: number) => void
  onRowClick: (id: string) => void
  onDelete: (id: string) => void
}

export function ApplicationTable({
  applications,
  currentPage,
  totalPages,
  totalItems,
  onPageChange,
  onRowClick,
  onDelete,
}: ApplicationTableProps) {
  if (applications.length === 0) {
    return (
      <Card>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">还没有创建任何应用</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>应用名称</TableHead>
              <TableHead>应用ID</TableHead>
              <TableHead>账号配额</TableHead>
              <TableHead>流量配额</TableHead>
              <TableHead>配额有效期</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {applications.map((app) => (
              <ApplicationRow key={app.id} application={app} onRowClick={onRowClick} onDelete={onDelete} />
            ))}
          </TableBody>
        </Table>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          onPageChange={onPageChange}
        />
      </CardContent>
    </Card>
  )
}

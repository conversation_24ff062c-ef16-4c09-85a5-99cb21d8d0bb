import { Button } from '@coozf/ui/components/button'
import { Plus } from 'lucide-react'

interface ApplicationListHeaderProps {
  onCreateClick: () => void
}

export function ApplicationListHeader({ onCreateClick }: ApplicationListHeaderProps) {
  return (
    <div className="flex justify-between items-center">
      <h1 className="text-3xl font-bold">应用管理</h1>
      <Button onClick={onCreateClick}>
        <Plus className="w-4 h-4 mr-2" />
        创建应用
      </Button>
    </div>
  )
}
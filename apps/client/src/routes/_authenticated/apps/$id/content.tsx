import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@coozf/ui/components/dialog'
import { CheckCircle, XCircle, Clock, AlertCircle, Eye, User, BarChart3 } from 'lucide-react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useQuery } from '@tanstack/react-query'
import { formatYearMonthDay } from '@coozf/ui/lib/day'
import { LoadingContainer } from '@coozf/ui/components/loading'

export const Route = createFileRoute('/_authenticated/apps/$id/content')({
  component: ContentListPage,
})

interface Metadata {
  title?: string
  desc?: string
  cover?: string
}

const statusMap = {
  PENDING: '待处理',
  PROCESSING: '处理中',
  COMPLETED: '已完成',
  FAILED: '失败',
  PARTIAL: '部分成功',
}

const statusIconMap = {
  PENDING: Clock,
  PROCESSING: Clock,
  COMPLETED: CheckCircle,
  FAILED: XCircle,
  PARTIAL: AlertCircle,
}

const statusColorMap = {
  PENDING: 'default',
  PROCESSING: 'secondary',
  COMPLETED: 'outline',
  FAILED: 'destructive',
  PARTIAL: 'destructive',
} as const

type PublishRecordDetailResponse = RouterOutput['publish']['detail']

function ContentListPage() {
  const { id } = Route.useParams()
  const [page, setPage] = useState(1)
  const [selectedPublishId, setSelectedPublishId] = useState<string | null>(null)

  // 获取发布记录列表
  const { data } = useQuery(
    trpc.publish.list.queryOptions({
      applicationId: id,
    }),
  )

  // 获取发布记录详情
  const { data: detailData, isLoading: detailLoading } = useQuery(
    trpc.publish.detail.queryOptions(
      { publishId: selectedPublishId!, applicationId: id },
      {
        enabled: !!selectedPublishId,
      },
    ),
  )

  const formatTraffic = (trafficGB: number) => {
    if (trafficGB >= 1) {
      return `${trafficGB.toFixed(1)}GB`
    } else {
      return `${(trafficGB * 1000).toFixed(0)}MB`
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link to="/apps/$id" params={{ id }} className="hover:text-foreground">
          应用详情
        </Link>
        <span>/</span>
        <span className="text-foreground">发布内容明细</span>
      </div>

      <Card>
        <CardContent className="p-0">
          {!data ? (
            <LoadingContainer />
          ) : data.data.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">暂无发布记录</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>发布内容</TableHead>
                  <TableHead>发布时间</TableHead>
                  <TableHead>发布状态</TableHead>
                  <TableHead>账号数量</TableHead>
                  <TableHead>任务进度</TableHead>
                  <TableHead>预计流量</TableHead>
                  <TableHead>实际流量</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.map((record) => {
                  const StatusIcon = statusIconMap[record.status]
                  const totalTasks = record.pendingCount + record.successCount + record.failedCount
                  const metadata = record.metadata as Metadata
                  return (
                    <TableRow key={record.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {metadata.cover && (
                            <img
                              src={metadata.cover}
                              alt={metadata.title || '发布内容'}
                              className="w-16 h-12 object-cover rounded border"
                              onError={(e) => {
                                e.currentTarget.src =
                                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA2NCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yOCAyNEwyOCAyNEwyOCAyNFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+'
                              }}
                            />
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                              {metadata.title || `发布记录 ${record.publishId}`}
                            </div>
                            {metadata.desc && (
                              <div className="text-xs text-muted-foreground truncate mt-1">{metadata.desc}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatYearMonthDay(record.createdAt)}</TableCell>
                      <TableCell>
                        <Badge variant={statusColorMap[record.status]} className="flex items-center gap-1 w-fit">
                          <StatusIcon className="h-3 w-3" />
                          {statusMap[record.status]}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.accountCount}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            {record.successCount + record.failedCount}/{totalTasks}
                          </div>
                          <div className="flex gap-1 text-xs">
                            {record.successCount > 0 && (
                              <span className="text-green-600">成功:{record.successCount}</span>
                            )}
                            {record.failedCount > 0 && <span className="text-red-600">失败:{record.failedCount}</span>}
                            {record.pendingCount > 0 && (
                              <span className="text-gray-600">待处理:{record.pendingCount}</span>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatTraffic(record.estimatedTraffic)}</TableCell>
                      <TableCell>{formatTraffic(record.actualTraffic)}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedPublishId(record.publishId)}>
                              <Eye className="h-4 w-4 mr-1" />
                              查看详情
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-h-[80vh] !max-w-[800px] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>发布记录详情</DialogTitle>
                            </DialogHeader>
                            <PublishDetailModal
                              publishId={selectedPublishId}
                              detailData={detailData}
                              isLoading={detailLoading}
                            />
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* 分页 */}
      {data && data.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            共 {data.total} 条记录，第 {data.page} / {data.totalPages} 页
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page <= 1}>
              上一页
            </Button>
            <Button variant="outline" size="sm" onClick={() => setPage(page + 1)} disabled={page >= data.totalPages}>
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

// 发布记录详情弹框组件
function PublishDetailModal({
  publishId,
  detailData,
  isLoading,
}: {
  publishId: string | null
  detailData: PublishRecordDetailResponse | undefined
  isLoading: boolean
}) {
  if (!publishId) return null

  if (isLoading) {
    return <LoadingContainer />
  }

  if (!detailData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">加载失败，请重试</p>
      </div>
    )
  }

  const record = detailData
  const metadata = record.metadata as Metadata
  const StatusIcon = statusIconMap[record.status]

  return (
    <div className="space-y-6">
      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            基本信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">发布ID</label>
              <p className="text-sm font-mono">{record.publishId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">发布时间</label>
              <p className="text-sm">{formatYearMonthDay(record.createdAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">状态</label>
              <Badge variant={statusColorMap[record.status]} className="flex items-center gap-1 w-fit">
                <StatusIcon className="h-3 w-3" />
                {statusMap[record.status]}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">账号数量</label>
              <p className="text-sm">{record.accountCount}</p>
            </div>
          </div>

          {metadata.title && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">标题</label>
              <p className="text-sm">{metadata.title}</p>
            </div>
          )}

          {metadata.desc && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">描述</label>
              <p className="text-sm">{metadata.desc}</p>
            </div>
          )}

          {metadata.cover && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">封面</label>
              <img src={metadata.cover} alt="封面" className="w-32 h-24 object-cover rounded border" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* 任务进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            任务进度
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{record.successCount}</div>
              <div className="text-sm text-muted-foreground">成功</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{record.failedCount}</div>
              <div className="text-sm text-muted-foreground">失败</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{record.pendingCount}</div>
              <div className="text-sm text-muted-foreground">待处理</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{record.successCount + record.failedCount + record.pendingCount}</div>
              <div className="text-sm text-muted-foreground">总计</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 流量统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            流量统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">预计流量</label>
              <p className="text-lg font-semibold">{record.estimatedTraffic}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">实际流量</label>
              <p className="text-lg font-semibold">{record.actualTraffic}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 账号详情 */}
      {record.trafficDetails && record.trafficDetails.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              账号详情
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>账号</TableHead>
                  <TableHead>平台</TableHead>
                  <TableHead>任务ID</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>流量使用</TableHead>
                  <TableHead>执行时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {record.trafficDetails.map((detail: any) => (
                  <TableRow key={detail.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {detail.authAccount.platformAvatar && (
                          <img src={detail.authAccount.platformAvatar} alt="头像" className="w-6 h-6 rounded-full" />
                        )}
                        <span className="text-sm">{detail.authAccount.platformUserName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {detail.authAccount.platformCode}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <code className="text-xs">{detail.taskId}</code>
                    </TableCell>
                    <TableCell>
                      <Badge variant={detail.success ? 'outline' : 'destructive'}>
                        {detail.success ? '成功' : '失败'}
                      </Badge>
                    </TableCell>
                    <TableCell>{detail.trafficUsed}</TableCell>
                    <TableCell>
                      <div className="text-xs text-muted-foreground">{formatYearMonthDay(detail.createdAt)}</div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

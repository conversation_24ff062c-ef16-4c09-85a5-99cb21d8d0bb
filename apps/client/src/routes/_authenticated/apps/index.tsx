import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { useMutation, useQuery } from '@tanstack/react-query'
import { LoadingContainer } from '@coozf/ui/components/loading'
import {
  ApplicationListHeader,
  CreateApplicationDialog,
  ApplicationSecretDialog,
  ApplicationTable,
} from '../../../components/app'


function ApplicationsPage() {
  const [page, setPage] = useState(1)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [showSecretDialog, setShowSecretDialog] = useState(false)
  const [newApplicationSecret, setNewApplicationSecret] = useState<string>('')
  const [newApplicationName, setNewApplicationName] = useState<string>('')
  const navigate = useNavigate()

  const {
    data: applicationsData,
    isLoading,
    refetch,
  } = useQuery(trpc.application.list.queryOptions({
    page,
    pageSize: 10,
  }))

  const deleteApplicationMutation = useMutation(trpc.application.delete.mutationOptions({
    onSuccess: () => {
      toast.success('应用删除成功')
      refetch()
    },
  }))

  const handleCreateSuccess = (data: { name: string; secret: string }) => {
    setNewApplicationSecret(data.secret)
    setNewApplicationName(data.name)
    setIsCreateDialogOpen(false)
    setShowSecretDialog(true)
    refetch()
  }

  const handleDelete = (id: string) => {
    deleteApplicationMutation.mutate({ applicationId: id })
  }

  const handleRowClick = (id: string) => {
    navigate({ to: '/apps/$id', params: { id } })
  }

  const handleSecretDialogClose = () => {
    setShowSecretDialog(false)
    setNewApplicationSecret('')
    setNewApplicationName('')
  }


  if (isLoading) {
    return <LoadingContainer />
  }

  return (
    <div className="space-y-6">
      <ApplicationListHeader onCreateClick={() => setIsCreateDialogOpen(true)} />
      
      <ApplicationTable
        applications={applicationsData?.data || []}
        currentPage={page}
        totalPages={applicationsData?.totalPages || 0}
        totalItems={applicationsData?.total || 0}
        onPageChange={setPage}
        onRowClick={handleRowClick}
        onDelete={handleDelete}
      />

      <CreateApplicationDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />

      <ApplicationSecretDialog
        open={showSecretDialog}
        onOpenChange={setShowSecretDialog}
        applicationName={newApplicationName}
        secret={newApplicationSecret}
        onClose={handleSecretDialogClose}
      />
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/')({
  component: ApplicationsPage,
})

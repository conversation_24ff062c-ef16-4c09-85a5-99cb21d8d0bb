/**
 * 安全地将 Decimal 类型转换为数字
 * 处理 Prisma Decimal 类型在序列化后可能是字符串的情况
 */
export function toNumber(value: any): number {
  if (typeof value === 'number') {
    return value
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? 0 : parsed
  }
  
  if (value && typeof value.toNumber === 'function') {
    return value.toNumber()
  }
  
  if (value && typeof value.toString === 'function') {
    const parsed = parseFloat(value.toString())
    return isNaN(parsed) ? 0 : parsed
  }
  
  return 0
}

/**
 * 格式化流量显示
 */
export function formatTraffic(trafficGB: any): string {
  const gb = toNumber(trafficGB)
  
  if (gb >= 1000) {
    return `${(gb / 1000).toFixed(1)}TB`
  } else if (gb >= 1) {
    return `${gb.toFixed(1)}GB`
  } else {
    return `${(gb * 1000).toFixed(0)}MB`
  }
}

/**
 * 格式化账号数量显示
 */
export function formatAccountCount(current: number, total: any): string {
  return `${current}/${toNumber(total)}`
}
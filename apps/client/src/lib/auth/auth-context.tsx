import { createContext, useContext } from 'react'

export interface AuthState {
  isAuthenticated: boolean
  user?: unknown
  isLoading: boolean
}

export interface AuthContextType extends AuthState {
  logout: () => void
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth 必须在 AuthProvider 内部使用')
  }
  return context
}

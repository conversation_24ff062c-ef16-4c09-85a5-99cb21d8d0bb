import { createFileRoute, redirect, Outlet, Link, useLocation } from '@tanstack/react-router'
import { useAuth } from '../lib/auth/auth-context'
import { ModeToggle } from '@/components/mode-toggle'
import { Avatar, AvatarFallback, AvatarImage } from '@coozf/ui/components/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import { authClient } from '@/lib/auth/auth-client'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@coozf/ui/components/sidebar'
import { Separator } from '@coozf/ui/components/separator'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@coozf/ui/components/breadcrumb'
import { Package2, Users, FileText, GitBranch, ShieldUser, UserRoundCog } from 'lucide-react'

export const Route = createFileRoute('/_authenticated')({
  component: AuthenticatedLayout,
})

// 导航菜单配置
const navigation = [
  // {
  //   title: '应用管理',
  //   items: [
  //     {
  //       title: '仪表板',
  //       url: '/',
  //       icon: Home,
  //     },
  //   ],
  // },
  {
    title: '业务管理',
    items: [
      {
        title: '用户管理',
        url: '/users',
        icon: Users,
      },
      {
        title: '应用管理',
        url: '/applications',
        icon: Package2,
      },
      {
        title: '媒体账号管理',
        url: '/auth-accounts',
        icon: ShieldUser,
      },
      {
        title: '订单管理',
        url: '/orders',
        icon: FileText,
      },
      {
        title: '后台用户管理',
        url: '/admin-user',
        icon: UserRoundCog,
      },
    ],
  },
  {
    title: '系统设置',
    items: [
      {
        title: '版本管理',
        url: '/versions',
        icon: GitBranch,
      },
      // {
      //   title: '系统配置',
      //   url: '/settings',
      //   icon: Settings,
      // },
    ],
  },
]

// 动态面包屑组件
function DynamicBreadcrumb() {
  const location = useLocation()

  // 根据路径生成面包屑
  const getBreadcrumbItems = () => {
    const path = location.pathname
    const pathSegments = path.split('/').filter(Boolean)

    // 路径映射
    const pathMap: Record<string, string> = {
      '': '仪表板',
      users: '用户管理',
      applications: '应用管理',
      'auth-accounts': '媒体账号管理',
      orders: '订单管理',
      versions: '版本管理',
      settings: '系统配置',
      'admin-user': '后台用户管理',
    }

    if (pathSegments.length === 0) {
      return [{ title: '仪表板', href: '/', isLast: true }]
    }

    const items = [{ title: '仪表板', href: '/', isLast: false }]

    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      const isLast = index === pathSegments.length - 1
      const title = pathMap[segment] || segment
      items.push({
        title,
        href: currentPath,
        isLast,
      })
    })

    return items
  }

  const breadcrumbItems = getBreadcrumbItems()

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <div key={item.href} className="flex items-center">
            {index > 0 && <BreadcrumbSeparator className="mx-2" />}
            <BreadcrumbItem>
              {item.isLast ? (
                <BreadcrumbPage>{item.title}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link to={item.href}>{item.title}</Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}

function AuthenticatedLayout() {
  const { logout } = useAuth()
  const { data: session } = authClient.useSession()
  const location = useLocation()

  const user = session?.user

  const handleLogout = () => {
    logout()
  }

  return (
    <SidebarProvider className="h-full">
      <Sidebar className="h-full">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link to="/">
                  <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                    <img src="/icon.svg" className="w-full h-full" />
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">开放平台</span>
                    <span className="truncate text-xs">后台管理</span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          {navigation.map((group) => (
            <SidebarGroup key={group.title}>
              <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {group.items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={location.pathname === item.url}>
                        <Link to={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src={user?.image || ''} alt={user?.username || '用户头像'} />
                      <AvatarFallback className="rounded-lg">
                        {user?.username?.[0]?.toUpperCase() || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">{user?.username || '未知用户'}</span>
                      <span className="truncate text-xs">{user?.email || '无邮箱'}</span>
                    </div>
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="bottom"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuItem asChild>
                    <Link to="/" className="cursor-pointer">
                      首页
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset className="h-full overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <DynamicBreadcrumb />
          <div className="ml-auto flex items-center space-x-4">
            <ModeToggle />
          </div>
        </header>
        <div className="flex overflow-hidden flex-1 flex-col gap-4 p-4">
          <Outlet />
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}

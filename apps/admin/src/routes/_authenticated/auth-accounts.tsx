import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useMemo, useState } from 'react'
import { trpc, type RouterOutput } from '@/lib/trpc'
import { useQuery, useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { DataTable } from '@/components/data-table'
import { TableSearch } from '@/components/auth-accounts/table-search'
import { DeleteConfirmDialog } from '@/components/auth-accounts/delete-confirm-dialog'
import { getTableColumns } from '@/components/auth-accounts/columns'
import { adminAccountListQuerySchema, type AdminAccountListQuery } from '@coozf/zod'

type AuthAccountItem = RouterOutput['authAccount']['list']['data'][number]

export const Route = createFileRoute('/_authenticated/auth-accounts')({
  component: AuthAccountsPage,
  validateSearch: (search) => adminAccountListQuerySchema.parse(search),
})

function AuthAccountsPage() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })

  const { search, platformCode, applicationId } = useSearch({ strict: false })

  const [filter, setFilter] = useState<Omit<AdminAccountListQuery, 'page' | 'pageSize'>>({
    search,
    platformCode,
    applicationId,
  })
  const [deleteAccountId, setDeleteAccountId] = useState<string | null>(null)

  // 获取账号列表
  const {
    data: accountsData,
    isLoading,
    refetch,
  } = useQuery(
    trpc.authAccount.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...filter,
    }),
  )

  // 删除账号
  const deleteAccountMutation = useMutation(
    trpc.authAccount.delete.mutationOptions({
      onSuccess: () => {
        toast.success('账号删除成功')
        setDeleteAccountId(null)
        refetch()
      },
      onError: (error) => {
        toast.error(error.message || '删除失败')
      },
    }),
  )

  const accounts = accountsData?.data || []

  const handleViewAccount = (account: AuthAccountItem) => {}

  const handleDeleteAccount = (accountId: string) => {
    setDeleteAccountId(accountId)
  }

  const confirmDeleteAccount = () => {
    if (deleteAccountId) {
      deleteAccountMutation.mutate({ id: deleteAccountId })
    }
  }

  const columns = useMemo(
    () => getTableColumns(handleViewAccount, handleDeleteAccount),
    [handleViewAccount, handleDeleteAccount],
  )

  return (
    <div className="flex flex-1 flex-col gap-6 overflow-hidden">
      {/* 搜索组件 */}
      <TableSearch
        values={filter}
        onSearch={(value) => {
          setPagination({ ...pagination, pageIndex: 0 })
          setFilter(value)
        }}
      />

      {/* 账号表格 */}
      <DataTable
        columns={columns}
        data={accounts}
        rowCount={accountsData?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={!!deleteAccountId}
        onOpenChange={() => setDeleteAccountId(null)}
        onConfirm={confirmDeleteAccount}
        isPending={deleteAccountMutation.isPending}
      />
    </div>
  )
}

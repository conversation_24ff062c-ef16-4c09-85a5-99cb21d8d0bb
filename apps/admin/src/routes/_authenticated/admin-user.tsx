import { getTableColumns } from '@/components/admin-user/columns'
import { CreateAdmin } from '@/components/admin-user/create-admin'
import { DataTable } from '@/components/data-table'
import { trpc } from '@/lib/trpc'
import { Button } from '@coozf/ui/components/button'
import { useMutation, useQuery } from '@tanstack/react-query'
import { createFileRoute } from '@tanstack/react-router'
import { Plus } from 'lucide-react'
import { useMemo, useState } from 'react'
import { toast } from 'sonner'

export const Route = createFileRoute('/_authenticated/admin-user')({
  component: RouteComponent,
})

function RouteComponent() {
  // 分页状态
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  })

  const [createDialogOpen, setCreateDialogOpen] = useState(false)

  // 删除用户
  const deleteMutation = useMutation(
    trpc.adminUser.delete.mutationOptions({
      onSuccess: () => {
        toast.success('用户删除成功')
      },
    }),
  )

  // 用户列表
  const { data, isLoading } = useQuery(
    trpc.adminUser.list.queryOptions({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    }),
  )

  // 设置密码
  const setPasswordMutation = useMutation(
    trpc.adminUser.setPassword.mutationOptions({
      onSuccess: () => {
        toast.success('密码设置成功')
      },
    }),
  )

  const columns = useMemo(
    () =>
      getTableColumns(
        (user) => {
          setPasswordMutation.mutate({
            id: user.id,
            password: '123456',
          })
        },
        (user) => {
          console.log(user)
        },
      ),
    [],
  )

  return (
    <div className="flex flex-1 flex-col gap-2 overflow-hidden">
      <div className="flex items-center justify-between">
        {/* <TableSearch
          values={filter}
          onSearch={(value) => {
            setPagination({ ...pagination, pageIndex: 0 })
            setFilter(value)
          }}
        /> */}
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="h-4 w-4" />
          创建用户
        </Button>
        <CreateAdmin
          open={createDialogOpen}
          setOpen={setCreateDialogOpen}
          onSuccess={() => setCreateDialogOpen(false)}
        />
      </div>
      <DataTable
        columns={columns}
        data={data?.data}
        rowCount={data?.total}
        pagination={pagination}
        setPagination={setPagination}
        isloading={isLoading}
      />
    </div>
  )
}

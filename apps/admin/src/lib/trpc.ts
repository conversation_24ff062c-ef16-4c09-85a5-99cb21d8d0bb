import type { AppRouter } from './../../../server/src/router/admin'
import { QueryClient } from '@tanstack/react-query'
import { httpBatchLink, createTRPCClient, TRPCClientError, loggerLink } from '@trpc/client'
import { createTRPCOptionsProxy } from '@trpc/tanstack-react-query'
import superjson from 'superjson'
import { authClient } from './auth/auth-client'
import { toast } from '@coozf/ui/lib/utils'
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server'
import { router } from '@/router'

export type ErrorContext = {
  error: Error
}

export type RouterInput = inferRouterInputs<AppRouter>
export type RouterOutput = inferRouterOutputs<AppRouter>

// 错误提示防抖机制
const errorToastCache = new Map<string, number>()
const ERROR_TOAST_DEBOUNCE = 3000 // 3秒内相同错误只显示一次

// 401错误处理标志，防止重复登出
let isLoggingOut = false

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        // 对于特定错误类型不进行重试
        if (error instanceof TRPCClientError) {
          const errorCode = error.data?.code
          const httpStatus = error.data?.httpStatus

          // 401认证错误、参数错误、禁止访问等不重试
          if (
            httpStatus === 401 ||
            httpStatus === 403 ||
            httpStatus === 422 ||
            errorCode?.includes('UNAUTHORIZED') ||
            errorCode?.includes('FORBIDDEN') ||
            errorCode?.includes('INVALID_INPUT') ||
            errorCode?.includes('INVALID_PARAMS')
          ) {
            // 在不重试的情况下，直接处理错误
            handleError(error)
            return false
          }
        }
        // 网络错误和服务器错误可以重试，最多2次
        if (failureCount >= 2) {
          // 重试次数用完，处理错误
          handleError(error as TRPCClientError<AppRouter>)
        }
        return failureCount < 2
      },
      // onError 在 queries 中不可用，错误处理在 retry 中进行
    },
    mutations: {
      retry: false, // 变更操作不重试
      onError: (error) => {
        handleError(error as TRPCClientError<AppRouter>)
      },
    },
  },
})

export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    loggerLink({
      enabled: (opts) =>
        (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') ||
        (opts.direction === 'down' && opts.result instanceof Error),
    }),
    httpBatchLink({
      url: import.meta.env.VITE_API_BASE_URL + '/api/trpc',
      transformer: superjson,
    }),
  ],
})

export const trpc = createTRPCOptionsProxy<AppRouter>({
  client: trpcClient,
  queryClient,
})

// 统一的错误处理函数
const handleError = (error: TRPCClientError<AppRouter>) => {
  // 错误日志上报
  console.error('=== API Error Handler Called ===')
  console.error('API Error:', error)
  console.error('Error data:', error.data)
  console.error('Error message:', error.message)
  console.error('Error httpStatus:', error.data?.httpStatus)
  console.error('Error code:', error.data?.code)

  if (error instanceof TRPCClientError) {
    const httpStatus = error.data?.httpStatus
    const errorCode = error.data?.code

    // 401认证错误处理
    if (httpStatus === 401 || errorCode?.includes('UNAUTHORIZED')) {
      handleAuthError()
      return
    }

    // 403权限错误
    if (httpStatus === 403 || errorCode?.includes('FORBIDDEN')) {
      showDebouncedToast('权限不足，请联系管理员', 'auth-error')
      return
    }

    // 422参数错误
    if (httpStatus === 422 || errorCode?.includes('INVALID_INPUT')) {
      showDebouncedToast('请求参数有误，请检查后重试', `param-error-${error.message}`)
      return
    }

    // 网络错误
    if (error.message.includes('NETWORK_ERROR') || error.message.includes('fetch')) {
      showDebouncedToast('网络连接错误，请检查网络后重试', 'network-error')
      return
    }

    // 服务器错误
    if (httpStatus && httpStatus >= 500) {
      showDebouncedToast('服务器错误，请稍后重试', 'server-error')
      return
    }
  }

  if ('error' in error) {
    const err = error.error
    if (err instanceof Object && 'message' in err) {
      showDebouncedToast((err.message as string) || '操作失败，请稍后重试', `general-error-${err.message}`)
    }
    return
  }

  // 默认错误提示
  showDebouncedToast(error.message || '操作失败，请稍后重试', `general-error-${error.message}`)
}

// 防抖错误提示
const showDebouncedToast = (message: string, key: string) => {
  const now = Date.now()
  const lastShown = errorToastCache.get(key)

  if (!lastShown || now - lastShown > ERROR_TOAST_DEBOUNCE) {
    toast.error(message)
    errorToastCache.set(key, now)
  }
}

// 处理认证错误
const handleAuthError = async () => {
  console.log('=== handleAuthError called ===')
  console.log('isLoggingOut:', isLoggingOut)

  if (isLoggingOut) return

  isLoggingOut = true

  try {
    console.log('Showing toast and signing out...')
    showDebouncedToast('登录已过期，请重新登录', 'auth-expired')
    await authClient.signOut()

    // 清除所有查询缓存
    queryClient.clear()

    // 重定向到登录页
    console.log('Navigating to login...')
    router.navigate({ to: '/login' })
  } catch (error) {
    console.error('Logout error:', error)
  } finally {
    // 重置标志，允许后续登出
    setTimeout(() => {
      isLoggingOut = false
    }, 1000)
  }
}

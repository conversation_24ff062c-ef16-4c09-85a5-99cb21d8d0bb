import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { CalendarRange } from '@coozf/ui/components/calendar-range'
import { omitBy } from 'lodash'
import type { RouterInput } from '@/lib/trpc'
import { SelectApp } from '../select-app'
import { platformOptions } from '@coozf/zod'

type AuthAccountSearchInput = RouterInput['authAccount']['list']

type SearchForm = Omit<AuthAccountSearchInput, 'startDate' | 'endDate'> & {
  createDateRange?: {
    from: Date
    to: Date
  }
}

export function TableSearch({
  values,
  onSearch,
}: {
  values?: AuthAccountSearchInput
  onSearch: (values: AuthAccountSearchInput) => void
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      search: values?.search?.trim() ?? '',
      platformCode: values?.platformCode,
      createDateRange: undefined,
    },
  })

  function onSubmit(values: SearchForm) {
    const params: AuthAccountSearchInput = {
      search: values.search?.trim(),
      platformCode: values?.platformCode,
      applicationId: values?.applicationId,
      startDate: values?.createDateRange?.from?.toISOString(),
      endDate: values.createDateRange?.to?.toISOString(),
    }
    const paramsOmitEmpty = omitBy(params, (value) => !value)
    onSearch(paramsOmitEmpty)
  }

  const resetFilters = () => {
    form.reset({
      search: '',
      platformCode: undefined,
      createDateRange: undefined,
    })
    onSearch({})
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="search"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>账号名称</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索账号名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="applicationId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>应用</FormLabel>
              <FormControl>
                <SelectApp value={field.value ?? ''} onChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="platformCode"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>平台</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择平台" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部平台</SelectItem>
                  {platformOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>添加时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
        <Button type="button" variant="outline" onClick={resetFilters}>
          重置
        </Button>
      </form>
    </Form>
  )
}

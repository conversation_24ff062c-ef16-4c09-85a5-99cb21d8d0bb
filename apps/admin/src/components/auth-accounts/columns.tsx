import type { RouterOutput } from '@/lib/trpc'
import { AvatarCard } from '@coozf/ui/components/avatar-card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import type { ColumnDef } from '@tanstack/react-table'
import { Eye, MoreHorizontal, Trash2 } from 'lucide-react'

type AuthAccountItem = RouterOutput['authAccount']['list']['data'][number]

const platformMap = {
  xiaohongshu: '小红书',
  douyin: '抖音',
  kuaishou: '快手',
  weibo: '微博',
}

// const platformIconMap = {
//   xiaohongshu: '🔴',
//   douyin: '⚫',
//   kuaishou: '🟡',
//   weibo: '🔴',
// }

export function getTableColumns(
  handleViewAccount: (account: AuthAccountItem) => void,
  handleDeleteAccount: (accountId: string) => void,
): ColumnDef<AuthAccountItem>[] {
  return [
    {
      accessorKey: 'account',
      header: '账号',
      cell: ({ row }) => {
        const account = row.original
        return (
          <div className="flex items-center gap-3">
            <AvatarCard
              src={account.platformAvatar ?? ''}
              title={account.platformUserName}
              subtitle={account.platformUserId}
            />
            {/* <div>
              <Avatar>
                <AvatarImage src={account.platformAvatar ?? ''} alt={account.platformUserName} />
                <AvatarFallback>{account.platformUserName.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <div className="font-medium">{account.platformUserName}</div>
              <div className="text-sm text-muted-foreground">{account.platformUserId}</div>
            </div> */}
          </div>
        )
      },
    },
    {
      accessorKey: 'platform',
      header: '平台',
      cell: ({ row }) => {
        const account = row.original
        return (
          <div className="flex items-center gap-2">
            <Badge>{account.platformCode}</Badge>
          </div>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      header: '添加时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.createdAt).toLocaleDateString('zh-CN')}
        </div>
      ),
    },
    {
      accessorKey: 'updatedAt',
      header: '更新时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.updatedAt).toLocaleDateString('zh-CN')}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const account = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleViewAccount(account)}>
                <Eye className="mr-2 h-4 w-4" />
                查看详情
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteAccount(account.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                删除账号
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}

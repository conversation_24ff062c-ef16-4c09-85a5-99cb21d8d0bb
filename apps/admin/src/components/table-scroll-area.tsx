import React from 'react';
import { ScrollArea } from '@coozf/ui/components/scroll-area';
import { cn } from '@coozf/ui/lib/utils';

const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
  <ScrollArea className="relative w-full overflow-hidden">
    <table
      ref={ref}
      className={cn('w-full caption-bottom text-sm', className)}
      {...props}
    />
  </ScrollArea>
));
Table.displayName = 'Table';

export { Table };

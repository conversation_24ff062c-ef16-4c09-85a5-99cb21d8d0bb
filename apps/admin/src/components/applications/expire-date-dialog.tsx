import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@coozf/ui/components/dialog'
import { Button } from '@coozf/ui/components/button'
import { Calendar } from '@coozf/ui/components/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@coozf/ui/components/popover'
import { CalendarIcon } from 'lucide-react'
import { cn } from '@coozf/ui/lib/utils'
import { formatBy } from '@coozf/ui/lib/day'
import type { RouterOutput } from '@/lib/trpc'
import { LoadingButton } from '@coozf/ui/components/loading'

type ApplicationItem = RouterOutput['application']['list']['items'][number]

interface ExpireDateDialogProps {
  application: ApplicationItem
  onUpdateExpireDate: (expireDate: number) => void
  isLoading?: boolean
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function ExpireDateDialog({
  application,
  onUpdateExpireDate,
  isLoading = false,
  open,
  onOpenChange,
}: ExpireDateDialogProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    application.accountQuotaExpireDate ? new Date(application.accountQuotaExpireDate) : undefined,
  )

  const handleSubmit = () => {
    if (selectedDate) {
      onUpdateExpireDate(selectedDate.getTime())
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>修改过期时间</DialogTitle>
          <DialogDescription>修改应用 "{application.name}" 的账户配额过期时间</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label className="text-sm font-medium">当前过期时间</label>
            <div className="text-sm text-muted-foreground">
              {application.accountQuotaExpireDate
                ? formatBy(application.accountQuotaExpireDate, 'YYYY年MM月DD日')
                : '未设置'}
            </div>
          </div>

          <div className="grid gap-2">
            <label className="text-sm font-medium">新的过期时间</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn('w-full justify-start text-left font-normal', !selectedDate && 'text-muted-foreground')}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? formatBy(selectedDate, 'YYYY年MM月DD日') : <span>选择日期</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  disabled={(date) => date < new Date()}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange?.(false)} disabled={isLoading}>
            取消
          </Button>
          <LoadingButton onClick={handleSubmit} isPending={isLoading} disabled={!selectedDate || isLoading}>
            确认更新
          </LoadingButton>
        </div>
      </DialogContent>
    </Dialog>
  )
}

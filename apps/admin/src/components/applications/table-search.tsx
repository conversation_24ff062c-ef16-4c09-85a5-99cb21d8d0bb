import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { omitBy } from 'lodash'
import { type ApplicationListParams } from '@coozf/zod'
import { SelectUser } from '../select-user'

type SearchForm = Omit<ApplicationListParams, 'page' | 'pageSize'>

export function TableSearch({ values, onSearch }: { values?: SearchForm; onSearch: (values: SearchForm) => void }) {
  const form = useForm<SearchForm>({
    defaultValues: {
      search: values?.search?.trim() ?? '',
      status: values?.status,
      userId: values?.userId,
    },
  })

  function onSubmit(values: SearchForm) {
    console.log(values)
    const params: SearchForm = {
      search: values.search?.trim(),
      status: values.status,
      userId: values.userId,
    }
    const paramsOmitEmpty = omitBy(params, (value) => !value)
    onSearch(paramsOmitEmpty)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="search"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>应用名称</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索应用名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>状态</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部</SelectItem>
                  <SelectItem value="ACTIVE">正常</SelectItem>
                  <SelectItem value="SUSPENDED">暂停</SelectItem>
                  <SelectItem value="DELETED">已删除</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="userId"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>用户</FormLabel>
              <FormControl>
                <SelectUser value={field.value ?? ''} onChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />
        <Button variant="outline" type="submit">搜索</Button>
      </form>
    </Form>
  )
}

import type { RouterOutput } from '@/lib/trpc'
import type { ColumnDef } from '@tanstack/react-table'
import { createActionColumn } from '../data-table'
import { Progress } from '@coozf/ui/components/progress'
import { AvatarCard } from '@coozf/ui/components/avatar-card'
import DropdownMenuComponent from '../dropdown-menu-component'
import { DropdownMenuItem } from '@coozf/ui/components/dropdown-menu'

type ApplicationItem = RouterOutput['application']['list']['data'][number]

export function getTableColumns(
  onEditExpireDate: (app: ApplicationItem) => void,
  onOverview: (app: ApplicationItem) => void,
): ColumnDef<ApplicationItem>[] {
  return [
    {
      accessorKey: 'appInfo',
      header: '应用信息',
      cell: ({ row }) => {
        const app = row.original
        return (
          <div className="flex flex-col gap-1">
            <div className="font-medium">{app.name}</div>
            <div className="flex items-center gap-2">
              <code className="text-xs text-muted-foreground rounded font-mono">{row.original.appId}</code>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'userId',
      header: '用户',
      cell: ({ row }) => {
        const user = row.original.user
        return <AvatarCard src={user.image} title={user.name} subtitle={user.phoneNumber} />
      },
    },
    {
      accessorKey: 'accountQuota',
      header: '账户配额',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.original.authAccountsCount} / {row.original.accountQuota}
          <Progress value={row.original.authAccountsCount} max={row.original.accountQuota || 1} />
        </div>
      ),
    },
    {
      accessorKey: 'trafficQuotaGB',
      header: '流量配额',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.original.trafficUsedGB} / {row.original.trafficQuotaGB} GB
          <Progress value={row.original.trafficUsedGB} max={row.original.trafficQuotaGB || 1} />
        </div>
      ),
    },
    {
      accessorKey: 'accountQuotaExpireDate',
      header: '到期时间',
      cell: ({ row }) => {
        if (!row.original.accountQuotaExpireDate) {
          return <span className="text-muted-foreground">未设置</span>
        }
        return new Date(row.original.accountQuotaExpireDate).toLocaleDateString('zh-CN')
      },
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => new Date(row.original.createdAt).toLocaleDateString('zh-CN'),
    },
    createActionColumn<ApplicationItem>((app) => (
      <DropdownMenuComponent>
        <DropdownMenuItem onClick={() => onEditExpireDate(app)}>修改过期时间</DropdownMenuItem>
        <DropdownMenuItem className="text-destructive hover:text-destructive" onClick={() => onOverview(app)}>
          数据
        </DropdownMenuItem>
      </DropdownMenuComponent>
    )),
  ]
}

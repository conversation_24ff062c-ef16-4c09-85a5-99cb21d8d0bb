import { trpc } from '@/lib/trpc'
import { SelectItem } from './select-item'

// 用户选择组件的简化版本
export function SelectUser({
  value,
  onChange,
  defaultValue,
  className,
}: {
  value?: string
  defaultValue?: string
  onChange: (value: string) => void
  className?: string
}) {
  return (
    <SelectItem
      value={value}
      defaultValue={defaultValue}
      onChange={onChange}
      queryOptions={trpc.user.list.queryOptions({ page: 1, pageSize: 100 })}
      dataExtractor={(data) => data?.data}
      labelKey="name"
      valueKey="id"
      placeholder="选择用户"
      emptyText="暂无用户"
      className={className}
    />
  )
}

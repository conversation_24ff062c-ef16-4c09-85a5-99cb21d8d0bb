import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { CalendarRange } from '@coozf/ui/components/calendar-range'
import { omitBy } from 'lodash'
import { type UserListInput } from '@coozf/zod'

type SearchForm = Omit<UserListInput, 'startDate' | 'endDate'> & {
  createDateRange?: {
    from: Date
    to: Date
  }
}

export function TableSearch({
  values,
  onSearch,
}: {
  values?: UserListInput
  onSearch: (values: UserListInput) => void
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      phone: values?.phone?.trim() ?? '',
      createDateRange: undefined,
    },
  })

  function onSubmit(values: SearchForm) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    console.log(values)
    const params: UserListInput = {
      phone: values.phone?.trim(),
      startDate: values?.createDateRange?.from?.getTime(),
      endDate: values.createDateRange?.to?.getTime(),
    }
    const paramsOmitEmpty = omitBy(params, (value) => !value)
    onSearch(paramsOmitEmpty)
  }
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>手机号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索手机号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">注册时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <Button variant="outline" type="submit">搜索</Button>
      </form>
    </Form>
  )
}

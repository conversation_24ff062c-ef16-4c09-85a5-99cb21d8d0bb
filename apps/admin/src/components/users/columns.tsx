import type { RouterOutput } from '@/lib/trpc'
import { AvatarCard } from '@coozf/ui/components/avatar-card'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import type { ColumnDef } from '@tanstack/react-table'
import { Circle, Settings, LogOut } from 'lucide-react'
import { createActionColumn } from '../data-table'
type UserItem = RouterOutput['user']['list']['data'][number]
export function getTableColumns(
  handleViewDevices: (user: UserItem) => void,
  handleForceOffline: (userId: string) => void,
): ColumnDef<UserItem>[] {
  return [
    {
      accessorKey: 'userInfo',
      header: '用户信息',
      cell: ({ row }) => {
        const user = row.original
        return <AvatarCard src={user.image} title={user.name} subtitle={user.phoneNumber} />
      },
    },
    {
      accessorKey: 'createdAt',
      header: '注册时间',
      cell: ({ row }) => new Date(row.original.createdAt).toLocaleDateString('zh-CN'),
    },
    {
      accessorKey: 'applicationCount',
      header: '应用数',
      cell: ({ row }) => <Badge variant="secondary">{row.original.applicationCount}</Badge>,
    },
    {
      accessorKey: 'onlineStatus',
      header: '在线状态',
      cell: ({ row }) => {
        const user = row.original
        return (
          <div>
            <div className="flex items-center gap-2">
              <Circle
                className={`h-2 w-2 ${user.isOnline ? 'fill-green-500 text-green-500' : 'fill-gray-400 text-gray-400'}`}
              />
              <Badge variant={user.isOnline ? 'default' : 'secondary'}>{user.isOnline ? '在线' : '离线'}</Badge>
              {user.isOnline && <span className="text-xs text-gray-500">{user.activeSessionCount} 设备</span>}
            </div>
            {user.lastActivity && (
              <div className="text-xs text-gray-400 mt-1">
                最后活动: {new Date(user.lastActivity).toLocaleString('zh-CN')}
              </div>
            )}
          </div>
        )
      },
    },
    createActionColumn<UserItem>((user) => (
      <div className="flex items-center justify-end gap-1">
        {user.isOnline && (
          <Button variant="ghost" size="sm" onClick={() => handleViewDevices(user)} title="设备管理">
            <Settings className="h-4 w-4" />
          </Button>
        )}
        {user.isOnline && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleForceOffline(user.id)}
            title="强制下线"
          >
            <LogOut className="h-4 w-4" />
          </Button>
        )}
      </div>
    )),
  ]
}

import { Button } from '@coozf/ui/components/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@coozf/ui/components/dialog'
import { Card, CardContent } from '@coozf/ui/components/card'
import { Circle, Globe, Monitor, Smartphone } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { useMutation, useQuery } from '@tanstack/react-query'
import { set } from 'lodash'
export const UserSession = ({
  open,
  onOpenChange,
  selectedUser,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedUser: any
}) => {
  // 获取用户设备列表
  const {
    data: userDevices,
    isLoading: isDevicesLoading,
    refetch: refetchDevices,
  } = useQuery(
    trpc.user.getOnlineStatus.queryOptions(
      {
        id: selectedUser?.id || '',
      },
      {
        enabled: !!selectedUser?.id,
      },
    ),
  )

  // 删除单个设备会话
  const deleteSessionMutation = useMutation(
    trpc.user.deleteSession.mutationOptions({
      onSuccess: () => {
        toast.success('设备已下线')
        refetchDevices()
      },
      onError: (error: any) => {
        toast.error(error.message || '设备下线失败')
      },
    }),
  )

  // 强制用户下线
  const forceOfflineMutation = useMutation(
    trpc.user.forceOffline.mutationOptions({
      onSuccess: () => {
        toast.success('用户已被强制下线')
        refetchDevices()
        onOpenChange(false)
      },
      onError: (error: any) => {
        toast.error(error.message || '强制下线失败')
      },
    }),
  )

  const getDeviceIcon = (userAgent: string | null) => {
    if (!userAgent) return <Globe className="h-4 w-4" />
    if (userAgent.includes('Mobile')) return <Smartphone className="h-4 w-4" />
    return <Monitor className="h-4 w-4" />
  }

  const getDeviceType = (userAgent: string | null) => {
    if (!userAgent) return '未知设备'
    if (userAgent.includes('Mobile')) return '移动设备'
    return '桌面设备'
  }

  const handleDeleteSession = (sessionId: string) => {
    if (selectedUser && window.confirm('确定要下线该设备吗？')) {
      deleteSessionMutation.mutate({
        userId: selectedUser.id,
        sessionId,
      })
    }
  }
  const handleForceOffline = (userId: string) => {
    if (window.confirm('确定要强制该用户下线吗？')) {
      forceOfflineMutation.mutate({ id: userId })
    }
  }
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>设备管理</DialogTitle>
          <DialogDescription>用户 {selectedUser?.phoneNumber || selectedUser?.name} 的在线设备</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {isDevicesLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">加载中...</p>
            </div>
          ) : userDevices?.activeSessions.length === 0 ? (
            <div className="text-center py-8">
              <Circle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">该用户当前没有在线设备</p>
            </div>
          ) : (
            <div className="space-y-3">
              {userDevices?.activeSessions.map((session: any) => (
                <Card key={session.id}>
                  <CardContent className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                      {getDeviceIcon(session.userAgent)}
                      <div>
                        <div className="font-medium text-sm">{getDeviceType(session.userAgent)}</div>
                        <div className="text-xs text-gray-500">IP: {session.ipAddress || '未知'}</div>
                        <div className="text-xs text-gray-500">
                          最后活动: {new Date(session.lastActivity).toLocaleString('zh-CN')}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteSession(session.id)}
                      disabled={deleteSessionMutation.isPending}
                    >
                      {deleteSessionMutation.isPending ? '下线中...' : '下线'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="flex justify-between gap-2 pt-4 border-t">
            <Button
              variant="destructive"
              onClick={() => handleForceOffline(selectedUser?.id || '')}
              disabled={forceOfflineMutation.isPending || !selectedUser || userDevices?.activeSessions.length === 0}
            >
              {forceOfflineMutation.isPending ? '处理中...' : '全部下线'}
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

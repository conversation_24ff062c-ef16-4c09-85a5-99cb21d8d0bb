import { Moon, Sun } from 'lucide-react'
import { Button } from '@coozf/ui/components/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@coozf/ui/components/dropdown-menu'
import { useTheme } from '@/components/theme-provider'

export function ModeToggle() {
  const { setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme('light')}>亮色</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>暗色</DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>系统</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

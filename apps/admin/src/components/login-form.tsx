'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@coozf/ui/components/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@coozf/ui/components/form'
import { toast } from 'sonner'
import { useMutation } from '@tanstack/react-query'
import { authClient } from '@/lib/auth/auth-client'
import { LoadingButton } from '@coozf/ui/components/loading'

// 手机登录表单验证 schema
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名'),
  password: z.string().min(1, '请输入密码'),
})
type LoginForm = z.infer<typeof loginSchema>

export function LoginForm() {
  // 手机登录表单
  const phoneForm = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  })

  // 手机号登录 mutation
  const phoneLoginMutation = useMutation({
    mutationFn: (values: LoginForm) => {
      return authClient.signIn.username(values)
    },
    onSuccess: async ({ data }) => {
      toast.success('登录成功')
    },
  })

  // const phoneSingupMutation = useMutation({
  //   mutationFn: (values: LoginForm) => {
  //     return authClient.signUp.email({
  //       name: values.username,
  //       username: values.username,
  //       email: `${values.username}@lj.com`,
  //       password: values.password,
  //     })
  //   },
  //   onSuccess: () => {
  //     toast.success('注册成功')
  //   },
  // })

  // 手机登录提交处理
  const onPhoneSubmit = (values: LoginForm) => {
    phoneLoginMutation.mutate(values)
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">登录账户</h1>
        <p className="text-muted-foreground text-sm text-balance">请输入您的用户名和密码</p>
      </div>

      <Form {...phoneForm}>
        <form onSubmit={phoneForm.handleSubmit(onPhoneSubmit)} className="flex flex-col gap-6">
          <FormField
            control={phoneForm.control}
            name="username"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>用户名</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="请输入您的用户名" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={phoneForm.control}
            name="password"
            render={({ field }) => (
              <FormItem className="grid gap-3">
                <FormLabel>密码</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input type="password" placeholder="请输入您的密码" className="flex-1" {...field} />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <LoadingButton
            type="submit"
            className="w-full"
            isPending={phoneLoginMutation.isPending}
            disabled={phoneLoginMutation.isPending}
          >
            登录
          </LoadingButton>
        </form>
      </Form>

      <div className="text-center text-sm text-muted-foreground">
        <p>
          登录即表示您同意我们的{' '}
          <a href="#" className="text-primary hover:underline">
            服务条款
          </a>{' '}
          和{' '}
          <a href="#" className="text-primary hover:underline">
            隐私政策
          </a>
        </p>
      </div>
    </div>
  )
}

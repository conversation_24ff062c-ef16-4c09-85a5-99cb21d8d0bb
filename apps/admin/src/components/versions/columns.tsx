import type { RouterOutput } from '@/lib/trpc'
import { Badge } from '@coozf/ui/components/badge'
import { Button } from '@coozf/ui/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@coozf/ui/components/dropdown-menu'
import { Switch } from '@coozf/ui/components/switch'
import type { ColumnDef } from '@tanstack/react-table'
import { Download, Edit, MoreHorizontal, Trash2 } from 'lucide-react'

type VersionItem = RouterOutput['version']['list']['items'][number]

const typeMap = {
  DESKTOP: '桌面端',
  BROWSER_PLUGIN: '浏览器插件',
  RPA: 'RPA',
  CRAWLER: '爬虫脚本',
}

const typeIconMap = {
  DESKTOP: '💻',
  BROWSER_PLUGIN: '🔌',
  RPA: '🤖',
  CRAWLER: '🕷️',
}

const platformMap = {
  WIN: 'Windows',
  MAC: 'macOS',
}

const platformIconMap = {
  WIN: '🪟',
  MAC: '🍎',
}

export function getTableColumns(
  handleEditVersion: (version: VersionItem) => void,
  handleDeleteVersion: (versionId: string) => void,
): ColumnDef<VersionItem>[] {
  return [
    {
      accessorKey: 'version',
      header: '版本号',
      cell: ({ row }) => {
        const version = row.original
        return (
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                <span className="text-xs">{typeIconMap[version.type]}</span>
              </div>
            </div>
            <div>
              <div className="font-mono font-medium">{version.version}</div>
              <div className="text-sm text-muted-foreground">{typeMap[version.type]}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: 'platform',
      header: '平台',
      cell: ({ row }) => {
        const platform = row.original.platform
        if (!platform) return <span className="text-muted-foreground">-</span>
        return (
          <div className="flex items-center gap-2">
            <span className="text-lg">{platformIconMap[platform]}</span>
            <span>{platformMap[platform]}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'forceUpdate',
      header: '强制更新',
      cell: ({ row }) => {
        const forceUpdate = row.original.forceUpdate
        return <Badge variant={forceUpdate ? 'destructive' : 'secondary'}>{forceUpdate ? '是' : '否'}</Badge>
      },
    },
    {
      accessorKey: 'publishedBy',
      header: '发布人',
      cell: ({ row }) => <div className="text-sm">{row.original.admin_user.name}</div>,
    },
    {
      accessorKey: 'publishedAt',
      header: '发布时间',
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {new Date(row.original.publishedAt).toLocaleString('zh-CN')}
        </div>
      ),
    },
    {
      accessorKey: 'description',
      header: '版本说明',
      cell: ({ row }) => {
        const description = row.original.description
        return <div className="max-w-xs truncate text-sm text-muted-foreground">{description || '-'}</div>
      },
    },
    {
      accessorKey: 'isActive',
      header: '状态',
      cell: ({ row }) => {
        const isActive = row.original.isActive
        return <Switch checked={isActive}>启用</Switch>
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const version = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => window.open(version.downloadUrl, '_blank')}>
                <Download className="mr-2 h-4 w-4" />
                下载
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditVersion(version)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteVersion(version.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    },
  ]
}

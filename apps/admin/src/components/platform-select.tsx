'use client'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { platformOptions } from '@coozf/zod'
import type { PlatformCode } from '@coozf/zod'

interface PlatformSelectProps {
  value?: PlatformCode
  onValueChange?: (value: PlatformCode | undefined) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function PlatformSelect({
  value,
  onValueChange,
  placeholder = '选择平台',
  className,
  disabled = false,
}: PlatformSelectProps) {
  const handleValueChange = (selectedValue: string) => {
    if (selectedValue === 'ALL') {
      onValueChange?.(undefined)
    } else {
      onValueChange?.(selectedValue as PlatformCode)
    }
  }

  return (
    <Select value={value ?? 'ALL'} onValueChange={handleValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="ALL">全部平台</SelectItem>
        {platformOptions.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

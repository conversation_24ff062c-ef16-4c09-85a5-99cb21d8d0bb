import { useForm } from 'react-hook-form'
import { Button } from '@coozf/ui/components/button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@coozf/ui/components/form'
import { Input } from '@coozf/ui/components/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@coozf/ui/components/select'
import { CalendarRange } from '@coozf/ui/components/calendar-range'
import { omitBy } from 'lodash'
import { Plus } from 'lucide-react'

type OrderSearchInput = {
  search?: string
  status?: 'PENDING' | 'COMPLETED' | 'CANCELLED'
  type?: 'PURCHASE' | 'GIFT'
  startDate?: string
  endDate?: string
}

type SearchForm = Omit<OrderSearchInput, 'startDate' | 'endDate'> & {
  createDateRange?: {
    from: Date
    to: Date
  }
}

export function TableSearch({
  values,
  onSearch,
}: {
  values?: OrderSearchInput
  onSearch: (values: OrderSearchInput) => void
}) {
  const form = useForm<SearchForm>({
    defaultValues: {
      search: values?.search?.trim() ?? '',
      status: values?.status,
      type: values?.type,
      createDateRange: undefined,
    },
  })

  function onSubmit(values: SearchForm) {
    const params: OrderSearchInput = {
      search: values.search?.trim(),
      status: values.status,
      type: values.type,
      startDate: values?.createDateRange?.from?.toISOString(),
      endDate: values.createDateRange?.to?.toISOString(),
    }
    const paramsOmitEmpty = omitBy(params, (value) => !value)
    onSearch(paramsOmitEmpty)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-x-4 gap-y-2 p-0.5">
        <FormField
          control={form.control}
          name="search"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>订单号</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="搜索订单号" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>状态</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部</SelectItem>
                  <SelectItem value="PENDING">待处理</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="CANCELLED">已取消</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>类型</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value === 'ALL' ? undefined : value)
                }}
                value={field.value || 'ALL'}
              >
                <FormControl>
                  <SelectTrigger className="w-24">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ALL">全部</SelectItem>
                  <SelectItem value="PURCHASE">购买</SelectItem>
                  <SelectItem value="GIFT">赠送</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="createDateRange"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>创建时间</FormLabel>
              <CalendarRange value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <Button variant="outline" type="submit">
          搜索
        </Button>
      </form>
    </Form>
  )
}

import type { RouterOutput } from '@/lib/trpc'
import { AvatarCard } from '@coozf/ui/components/avatar-card'
import { Badge } from '@coozf/ui/components/badge'
import type { ColumnDef } from '@tanstack/react-table'
import { createActionColumn } from '../data-table'
import { Switch } from '@coozf/ui/components/switch'
import DropdownMenuComponent from '../dropdown-menu-component'
import { DropdownMenuItem } from '@coozf/ui/components/dropdown-menu'
type UserItem = RouterOutput['adminUser']['list']['data'][number]
export function getTableColumns(
  handleResetPassword: (user: UserItem) => void,
  handleSetRole: (user: UserItem) => void,
): ColumnDef<UserItem>[] {
  return [
    {
      accessorKey: 'userInfo',
      header: '用户信息',
      cell: ({ row }) => {
        const user = row.original
        return <AvatarCard src={user.image} title={user.name} subtitle={user.id} />
      },
    },
    {
      accessorKey: 'username',
      header: '用户名',
      cell: ({ row }) => row.original.username,
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => new Date(row.original.createdAt).toLocaleDateString('zh-CN'),
    },
    {
      accessorKey: 'role',
      header: '角色',
      cell: ({ row }) => <Badge variant="secondary">{row.original.role}</Badge>,
    },
    {
      accessorKey: 'onlineStatus',
      header: '双重认证',
      cell: ({ row }) => {
        const user = row.original
        return (
          <div>
            <Switch checked={user.twoFactorEnabled ?? false} />
          </div>
        )
      },
    },
    createActionColumn<UserItem>((user) => (
      <DropdownMenuComponent>
        <DropdownMenuItem onClick={() => handleResetPassword(user)}>修改密码</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSetRole(user)}>设置角色</DropdownMenuItem>
      </DropdownMenuComponent>
    )),
  ]
}

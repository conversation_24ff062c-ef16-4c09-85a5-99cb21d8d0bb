// Prisma schema file for MySQL database
// 从 Drizzle ORM + PostgreSQL 迁移到 Prisma ORM + MySQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String        @id @default(cuid())
  name                String        @db.Text
  email               String
  emailVerified       Boolean
  image               String?       @db.Text
  createdAt           DateTime
  updatedAt           DateTime
  phoneNumber         String?
  phoneNumberVerified Boolean?
  sessions            Session[]
  accounts            Account[]
  applications        Application[]

  @@unique([email])
  @@unique([phoneNumber])
  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?  @db.Text
  userAgent String?  @db.Text
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String    @db.Text
  providerId            String    @db.Text
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @db.Text
  refreshToken          String?   @db.Text
  idToken               String?   @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?   @db.Text
  password              String?   @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id @default(cuid())
  identifier String    @db.Text
  value      String    @db.Text
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// 应用表
model Application {
  id            String            @id @default(cuid())
  userId        String
  appId         String            @unique @db.VarChar(64) // 公开的应用ID
  name          String            @db.VarChar(100)
  description   String?           @db.Text
  secret        String            @db.VarChar(255) // 加密存储的应用密钥
  status        ApplicationStatus @default(ACTIVE) // active, suspended, deleted
  webhookUrl    String?           @db.VarChar(500) // 应用的webhook回调地址
  webhookSecret String            @db.VarChar(255) // 用于签名的webhook密钥

  // 配额相关字段
  accountQuota           Int       @default(0) // 账号配额
  trafficQuotaGB         Decimal   @default(0.00) @db.Decimal(10, 2) // 流量配额(GB)
  trafficUsedGB          Decimal   @default(0.00) @db.Decimal(10, 2) // 已使用流量(GB)
  accountQuotaExpireDate DateTime? // 账号配额到期日期

  // OEM相关字段
  oemEnabled Boolean @default(false) // OEM功能开关
  oemConfig  Json? // OEM配置信息

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  authAccounts   AuthAccount[]
  apiCalls       ApiCall[]
  orders         Order[]
  publishRecords PublishRecord[]
  trafficUsages  TrafficUsage[]
  clientVersions ClientVersion[] // OEM客户端版本

  @@index([userId])
  @@index([appId])
  @@index([status])
  @@map("applications")
}

// 应用状态枚举
enum ApplicationStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

// 授权账号表
model AuthAccount {
  id                 String   @id @default(cuid())
  applicationId      String
  platformCode       String   @db.VarChar(20) // xiaohongshu, douyin, kuaishou 等
  platformUserId     String   @db.VarChar(100)
  platformUserName   String   @db.VarChar(100) // 用户在平台的名称
  platformAvatar     String?  @db.VarChar(500) // 用户头像URL
  
  authType           AccountAuthType   @default(COOKIE) // 授权类型：COOKIE, TOKEN
  // 平台cookie hash
  platformCookieHash String   @db.LongText // 平台cookie的哈希值
  platformTokenExpiresAt DateTime?     // 平台访问令牌过期时间

  // 是否已删除
  isDeleted          Boolean  @default(false) // 是否已删除
  state              String?  @db.VarChar(100) // OAuth状态参数
  scope              String?  @db.VarChar(200) // 授权范围
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // 关系
  application   Application    @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  trafficUsages TrafficUsage[]

  @@index([applicationId])
  @@index([platformUserId])
  @@map("auth_accounts")
}

// 平台账号授权类型
enum AccountAuthType {
  COOKIE
  TOKEN
}

// API调用记录表
model ApiCall {
  id            String      @id @default(cuid())
  applicationId String
  endpoint      String      @db.VarChar(255)
  method        String      @db.VarChar(10)
  costType      ApiCostType // TRAFFIC, API_CALL
  costAmount    Decimal     @db.Decimal(10, 2)
  statusCode    Int?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([endpoint])
  @@index([costType])
  @@index([createdAt])
  @@map("api_calls")
}

// API调用成本类型枚举
enum ApiCostType {
  ACCOUNT_QUOTA
  TRAFFIC
}

// 订单表
model Order {
  id               String        @id @default(cuid())
  orderNo          String        @unique @db.VarChar(32) // 订单号
  applicationId    String
  amount           Decimal       @db.Decimal(10, 2) // 对应金额
  source           OrderSource   @default(SYSTEM) // 来源：SYSTEM(系统订单)
  type             OrderType // 类型：PURCHASE(购买), GIFT(赠送)
  paymentMethod    PaymentMethod @default(NONE) // 付款方式：BANK_TRANSFER(对公转账)
  status           OrderStatus   @default(COMPLETED) // 状态：PENDING(待付款), COMPLETED(已完成), CANCELLED(已取消)
  invoiceRequested Boolean       @default(false) // 发票申请标识
  remarks          String?       @db.VarChar(500) // 备注说明

  // 配额相关字段
  quotaType   QuotaType // 配额类型：ACCOUNT(账号), TRAFFIC(流量)
  quotaAmount Decimal   @db.Decimal(10, 2) // 配额数量

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([orderNo])
  @@index([status])
  @@index([type])
  @@index([createdAt])
  @@map("orders")
}

// 订单来源枚举
enum OrderSource {
  SYSTEM
}

// 订单类型枚举
enum OrderType {
  PURCHASE // 购买
  GIFT // 赠送
}

// 付款方式枚举
enum PaymentMethod {
  BANK_TRANSFER
  // 支付宝
  ALI_PAY
  // 微信支付
  WECHAT_PAY
  // 余额支付
  BALANCE
  // 暂无
  NONE
}

// 订单状态枚举
enum OrderStatus {
  PENDING
  COMPLETED
  CANCELLED
}

// 配额类型枚举
enum QuotaType {
  ACCOUNT
  TRAFFIC
}

// 后台管理系统

model Admin_user {
  id               String          @id @default(cuid())
  name             String          @db.Text
  email            String
  emailVerified    Boolean
  image            String?         @db.Text
  createdAt        DateTime
  updatedAt        DateTime
  twoFactorEnabled Boolean?
  username         String?
  displayUsername  String?         @db.Text
  role             String?         @db.Text
  banned           Boolean?
  banReason        String?         @db.Text
  sessions         Admin_session[]
  accounts         Admin_account[]
  twoFactor        TwoFactor[]
  versions         Version[]

  @@unique([email])
  @@unique([username])
  @@map("admin_user")
}

model Admin_session {
  id             String     @id @default(cuid())
  expiresAt      DateTime
  token          String
  createdAt      DateTime
  updatedAt      DateTime
  ipAddress      String?    @db.Text
  userAgent      String?    @db.Text
  userId         String
  impersonatedBy String?    @db.Text
  admin_user     Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("admin_session")
}

model Admin_account {
  id                    String     @id @default(cuid())
  accountId             String     @db.Text
  providerId            String     @db.Text
  userId                String
  admin_user            Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?    @db.Text
  refreshToken          String?    @db.Text
  idToken               String?    @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?    @db.Text
  password              String?    @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("admin_account")
}

model Admin_verification {
  id         String    @id @default(cuid())
  identifier String    @db.Text
  value      String    @db.Text
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("admin_verification")
}

model TwoFactor {
  id          String     @id
  secret      String     @db.Text
  backupCodes String     @db.Text
  userId      String
  admin_user  Admin_user @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("twoFactor")
}

// 版本管理表
model Version {
  id             String          @id @default(cuid())
  version        String          @db.VarChar(20) // 版本号，如：1.0.1
  type           VersionType // 版本类型：DESKTOP(桌面端), BROWSER_PLUGIN(浏览器插件)
  platform       Platform? // 平台：WIN, MAC (仅桌面端需要)
  downloadUrl    String          @db.VarChar(500) // 下载地址
  forceUpdate    Boolean         @default(false) // 是否强制更新
  description    String?         @db.Text // 版本公告/更新内容
  publishedId    String // 发布人
  publishedAt    DateTime        @default(now()) // 发布时间
  isActive       Boolean         @default(true) // 是否启用
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  admin_user     Admin_user      @relation(fields: [publishedId], references: [id], onDelete: Cascade)
  clientVersions ClientVersion[]

  @@unique([version, type, platform]) // 版本号+类型+平台的组合唯一
  @@index([type])
  @@index([platform])
  @@index([isActive])
  @@index([publishedAt])
  @@map("versions")
}

// 版本类型枚举
enum VersionType {
  DESKTOP // 桌面端
  BROWSER_PLUGIN // 浏览器插件
  RPA
  CRAWLER
}

// 平台枚举
enum Platform {
  WIN // Windows
  MAC // macOS
}

// 发布记录表 - 主表，用于列表查询
model PublishRecord {
  id            String @id @default(cuid())
  applicationId String
  publishId     String @unique @db.VarChar(64) // 发布ID

  // 流量统计
  estimatedTraffic Decimal @db.Decimal(10, 2) // 预计流量（提交时计算）
  actualTraffic    Decimal @default(0.00) @db.Decimal(10, 2) // 实际流量（回调后计算）

  // 任务统计
  accountCount Int @default(0) // 账号数量
  pendingCount Int @default(0) // 待处理数量
  successCount Int @default(0) // 成功数量
  failedCount  Int @default(0) // 失败数量

  status    PublishStatus @default(PENDING)
  metadata  Json // 概要信息
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // 关系
  application    Application    @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  trafficDetails TrafficUsage[]

  @@index([applicationId])
  @@index([publishId])
  @@index([status])
  @@index([createdAt])
  @@map("publish_records")
}

// 流量使用记录表 - 详细记录，按需查询
model TrafficUsage {
  id              String @id @default(cuid())
  publishRecordId String // 关联发布记录
  applicationId   String
  authAccountId   String

  taskId String @unique @db.VarChar(64) // 外部任务ID，用于webhook回调

  // 流量信息
  estimatedTraffic Decimal @db.Decimal(10, 2) // 预计流量
  actualTraffic    Decimal @default(0.00) @db.Decimal(10, 2) // 实际扣除流量

  status       TaskStatus @default(PENDING)
  errorMessage String?    @db.Text
  metadata     Json // 详细信息

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  publishRecord PublishRecord @relation(fields: [publishRecordId], references: [id], onDelete: Cascade)
  application   Application   @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  authAccount   AuthAccount   @relation(fields: [authAccountId], references: [id], onDelete: Cascade)

  @@index([publishRecordId])
  @@index([applicationId])
  @@index([authAccountId])
  @@index([taskId])
  @@index([status])
  @@index([createdAt])
  @@map("traffic_usage")
}

// 发布状态枚举
enum PublishStatus {
  PENDING // 待处理
  PROCESSING // 处理中
  COMPLETED // 已完成
  FAILED // 失败
  PARTIAL // 部分成功
}

// 任务状态枚举
enum TaskStatus {
  PENDING // 待处理
  SUCCESS // 成功
  FAILED // 失败
}

// 客户端构建状态枚举
enum ClientBuildStatus {
  PENDING // 待构建
  BUILDING // 构建中
  SUCCESS // 构建成功
  FAILED // 构建失败
  CANCELLED // 已取消
}

// OEM客户端版本表
model ClientVersion {
  id            String @id @default(cuid())
  applicationId String
  baseVersionId String

  platform    Platform // 平台
  description String?  @db.Text // 描述

  // 构建状态
  buildStatus  ClientBuildStatus @default(PENDING)
  downloadUrl  String?           @db.VarChar(500)
  buildLog     String?           @db.LongText
  errorMessage String?           @db.Text

  // GitLab集成
  gitlabProjectId  String? @db.VarChar(100) // GitLab项目ID
  gitlabPipelineId String? @db.VarChar(100) // Pipeline ID
  gitlabJobId      String? @db.VarChar(100) // Job ID  
  gitlabCommitSha  String? @db.VarChar(100) // Commit SHA
  gitlabBranch     String? @db.VarChar(100) // 构建分支
  gitlabWebUrl     String? @db.VarChar(500) // GitLab Web URL

  // 时间戳
  buildStartedAt   DateTime? // 构建开始时间
  buildCompletedAt DateTime? // 构建完成时间  
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // 关系
  application Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  baseVersion Version     @relation(fields: [baseVersionId], references: [id])

  // @@unique([applicationId, platform]) // 临时注释掉，等清理重复数据后再启用
  @@index([applicationId])
  @@index([buildStatus])
  @@index([createdAt])
  @@map("client_versions")
}

import { PrismaClient } from '@prisma/client'
import { env } from './env'

// 创建 Prisma 客户端实例
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    datasources: {
      db: {
        url: env.DATABASE_URL,
      },
    },
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 测试数据库连接
prisma
  .$connect()
  .then(() => {
    console.log('✅ MySQL 数据库连接成功')
  })
  .catch((error: unknown) => {
    console.error('❌ MySQL 数据库连接失败:', error)
    process.exit(1) // 如果数据库连接失败，终止程序
  })

// 优雅关闭数据库连接
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

{"name": "@coozf/auth", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./src/index.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "generate": "dotenv -e ../../.env -- pnpx @better-auth/cli@latest generate", "generate:admin": "dotenv -e ../../.env -- pnpx @better-auth/cli@latest generate --config ./src/lib/admin.ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@coozf/db": "workspace:*", "better-auth": "^1.2.9", "zod": "catalog:", "dotenv-cli": "catalog:"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/prettier-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "fastify": "catalog:"}, "prettier": "@coozf/prettier-config"}
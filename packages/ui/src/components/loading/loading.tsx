import { cn } from '../../lib/utils'
import React from 'react'

type LoadingProps = React.HTMLProps<HTMLDivElement>
export function Loading({ className, ...props }: LoadingProps) {
  return (
    <div
      {...props}
      className={cn(
        'size-7 animate-spin rounded-full border-[3px] border-muted-foreground border-l-transparent',
        className,
      )}
    />
  )
}

export function LoadingContainer({ className, ...props }: LoadingProps) {
  return (
    <div className="flex w-full flex-1 items-center justify-center">
      <Loading className={cn('', className)} {...props} />
    </div>
  )
}

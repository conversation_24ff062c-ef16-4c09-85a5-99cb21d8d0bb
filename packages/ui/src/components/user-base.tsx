import { Avatar, AvatarFallback, AvatarImage } from './avatar'
import { cn } from '@/lib/utils'

export interface AccountUser {
  id: string | number
  name: string
  avatar: string
  platformComponent?: React.ReactNode
  subtitle?: string
}

export function UserBase({
  size,
  platformComponent,
  subtitle,
  ...props
}: AccountUser & {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
}) {
  return (
    <div className="flex items-center gap-2 cursor-pointer">
      <div className="flex-shrink-0 relative">
        <Avatar
          className={cn(
            'w-7 h-7',
            size === 'sm' && 'w-4 h-4',
            size === 'md' && 'w-7 h-7',
            size === 'lg' && 'w-8 h-8',
            size === 'xl' && 'w-10 h-10',
            size === '2xl' && 'w-16 h-16',
          )}
        >
          <AvatarImage src={props.avatar} />
          <AvatarFallback className="text-xs">{props.name?.substring(0, 2)}</AvatarFallback>
        </Avatar>
        {platformComponent}
      </div>
      <div>
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          <span className={cn('flex-grow-0 flex-shrink-1 line-clamp-1')} style={{ maxWidth: 'clac(100% - 20px)' }}>
            {props.name}
          </span>
        </div>
        {subtitle && <div className="text-xs text-muted-foreground">{subtitle}</div>}
      </div>
    </div>
  )
}

export default UserBase

{"name": "@coozf/ui", "version": "0.0.0", "private": true, "type": "module", "license": "MIT", "sideEffects": ["**/*.css"], "files": ["dist"], "exports": {".": "./dist/index.js", "./styles.css": "./dist/index.css", "./*": {"types": "./dist/src/*.d.ts", "default": "./dist/src/*"}}, "scripts": {"build:styles": "tailwindcss -i ./src/styles/globals.css -o ./dist/index.css", "build:components": "tsc && tsc-alias", "build": "pnpm run build:components && pnpm run build:styles", "check-types": "tsc --noEmit", "dev:styles": "tailwindcss -i ./src/styles/globals.css -o ./dist/index.css --watch", "dev:components": "tsc --watch", "lint": "eslint src --max-warnings 0", "ui:add": "shadcn add"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@radix-ui/react-visually-hidden": "^1.2.3", "@swc/helpers": "^0.5.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.518.0", "next-themes": "^0.4.6", "react": "catalog:", "react-day-picker": "^9.8.0", "react-hook-form": "^7.59.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "vaul": "^1.1.2", "zod": "catalog:"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/tsconfig": "workspace:*", "@tailwindcss/cli": "^4.1.5", "@types/node": "^22.15.3", "@types/react": "catalog:", "tailwindcss": "^4.1.10", "tsc-alias": "^1.8.16", "typescript": "5.8.2"}}
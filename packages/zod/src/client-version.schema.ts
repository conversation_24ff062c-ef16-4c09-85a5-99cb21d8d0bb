import { z } from 'zod'
import { VersionTypeSchema, PlatformSchema } from './version.scheam'

// 客户端构建状态枚举
export const ClientBuildStatusSchema = z.enum(['PENDING', 'BUILDING', 'SUCCESS', 'FAILED', 'CANCELLED'])

// OEM配置Schema
export const OEMConfigSchema = z.object({
  // 基本信息
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  shortName: z.string().min(1, '简短名称不能为空').max(50, '简短名称不能超过50个字符'),
  logoUrl: z.string().url('Logo URL格式错误').optional(),
  homeUrl: z.string().url('首页地址格式错误').optional(),
  packageName: z.string().max(100, '包名不能超过100个字符').optional(),
})

// 创建客户端版本Schema
export const CreateClientVersionSchema = z.object({
  applicationId: z.string(),
  baseVersionId: z.string(),
  platform: PlatformSchema,
  description: z.string().optional(),
})

// 更新客户端版本构建状态Schema
export const UpdateClientVersionBuildStatusSchema = z.object({
  id: z.string(),
  buildStatus: ClientBuildStatusSchema,
  downloadUrl: z.string().url().optional(),
  buildLog: z.string().optional(),
  errorMessage: z.string().optional(),
  gitlabPipelineId: z.string().optional(),
  gitlabJobId: z.string().optional(),
  gitlabCommitSha: z.string().optional(),
  gitlabBranch: z.string().optional(),
  gitlabWebUrl: z.string().url().optional(),
  buildStartedAt: z.date().optional(),
  buildCompletedAt: z.date().optional(),
})

// 客户端版本列表查询Schema
export const ClientVersionListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  buildStatus: ClientBuildStatusSchema.optional(),
  platform: PlatformSchema.optional(),
})

// 触发构建Schema
export const TriggerBuildSchema = z.object({
  clientVersionId: z.string(),
})

// 扩展checkUpdate参数Schema，增加appId支持
export const CheckUpdateWithAppSchema = z.object({
  type: VersionTypeSchema,
  platform: PlatformSchema.optional(),
  version: z.string().regex(/^\d+\.\d+\.\d+$/, '版本号格式应为 x.y.z').optional(),
  appId: z.string().optional(), // 新增：应用ID，用于OEM版本检查
}).refine((data) => {
  // 桌面端必须指定平台
  if (data.type === 'DESKTOP') {
    return !!data.platform
  }
  return true
}, {
  message: '桌面端必须指定平台'
})

// 类型导出
export type ClientBuildStatus = z.infer<typeof ClientBuildStatusSchema>
export type OEMConfig = z.infer<typeof OEMConfigSchema>
export type CreateClientVersionInput = z.infer<typeof CreateClientVersionSchema>
export type UpdateClientVersionBuildStatusInput = z.infer<typeof UpdateClientVersionBuildStatusSchema>
export type ClientVersionListParams = z.infer<typeof ClientVersionListSchema>
export type TriggerBuildInput = z.infer<typeof TriggerBuildSchema>
export type CheckUpdateWithAppInput = z.infer<typeof CheckUpdateWithAppSchema>


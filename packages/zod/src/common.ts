import z from "zod";

// 分页通用
export const PaginationSchema = z.object({
  page: z.number().min(1, '页码必须大于0').optional().default(1),
  pageSize: z.number().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').optional().default(10),
})

export const createOpenApiResponseSchema = <T extends z.ZodType>(dataSchema: T) => {
  return z.object({
    code: z.number().describe('状态码，0表示成功，其他表示错误'),
    message: z.string().describe('操作结果消息'),
    data: dataSchema.describe('返回数据'),
  }).describe('OpenAPI通用响应格式')
}

// 分页响应格式
export const createPaginationResponseSchema = <T extends z.ZodType>(dataSchema: T) =>z.object({
  data: z.array(dataSchema).describe('返回数据'),
  total: z.number().describe('总数'),
  page: z.number().describe('当前页'),
  pageSize: z.number().describe('页大小'),
  totalPages: z.number().describe('总页数'),
})

// 时间查询通用
export const dateRangeSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// 更新时间查询通用
export const updateDateRangeSchema = z.object({
  startUpdateDate: z.string().optional(),
  endUpdateDate: z.string().optional(),
})

// 分页类型
export type PaginationParams = z.infer<typeof PaginationSchema>

export type DateRangeParams = z.infer<typeof dateRangeSchema>

export type UpdateDateRangeParams = z.infer<typeof updateDateRangeSchema>
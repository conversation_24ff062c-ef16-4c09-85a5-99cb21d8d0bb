import { z } from 'zod'

const dbEnvSchema = z.object({
    DATABASE_URL: z.string(),
    DEFAULT_TIMEZONE: z.string().default('Asia/Shanghai'),
    REDIS_HOST: z.string().default('localhost'),
    REDIS_PORT: z.string().transform(Number).default('6379'),
    REDIS_PASSWORD: z.string().default(''),
    REDIS_USERNAME: z.string().default('default'),
    REDIS_DB: z.string().transform(Number).default('0'),
})

const huoshanEnvSchema = z.object({
    OSS_ACCESS_KEY_ID: z.string(),
    OSS_ACCESS_KEY_SECRET: z.string(),
    OSS_DEFAULT_BUCKET: z.string().default('lingjing'),
    OSS_DESKTOP_BUCKET: z.string().default('yixiaoer-lite-desktop-download'),
    TLS_SECRET_ACCESS_KEY: z.string(),
    TLS_ACCESS_KEY_ID: z.string(),
    TLS_HOST: z.string(),
    TLS_REGION: z.string(),
    TLS_LOG_TOPIC_ID: z.string(),
})

export { dbEnvSchema, huoshanEnvSchema }
import z from "zod"

// 认证请求 Schema
const AuthenticateSchema = z.object({
  appId: z.string().min(1, 'App ID 不能为空').describe('应用唯一标识符，格式：app_xxxxxxxx'),
  secret: z.string().min(1, 'Secret 不能为空').describe('应用密钥，格式：sk_xxxxxxxx'),
})

// 认证响应 Schema
const AuthenticateResponseSchema = z.object({
  access_token: z.string().describe('访问令牌，用于后续API调用的身份验证'),
  token_type: z.string().describe('令牌类型，固定值：Bearer'),
  expires_in: z.number().describe('令牌过期时间，单位：秒'),
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      appId: z.string().describe('应用唯一标识符'),
    })
    .describe('应用信息'),
})

// Token验证响应 Schema
const TokenVerifyResponseSchema = z.object({
  valid: z.boolean().describe('Token是否有效'),
  user: z
    .object({
      id: z.string().describe('用户数据库ID'),
      name: z.string().describe('用户名称'),
      phone: z.string().optional().describe('用户手机号'),
    })
    .describe('用户信息'),
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      appId: z.string().describe('应用唯一标识符'),
    })
    .describe('应用信息'),
})

// 应用信息响应 Schema
const AppInfoResponseSchema = z.object({
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      description: z.string().optional().describe('应用描述'),
      appId: z.string().describe('应用唯一标识符'),

      createdAt: z.string().describe('创建时间'),
    })
    .describe('应用详细信息'),
  user: z
    .object({
      id: z.string().describe('用户数据库ID'),
      name: z.string().describe('用户名称'),
    })
    .describe('用户信息'),
})

export {
  AuthenticateSchema,
  AuthenticateResponseSchema,
  TokenVerifyResponseSchema,
  AppInfoResponseSchema,
}
import z from "zod";

export const clientIdSchema = z.object({
  clientId: z.string().optional().nullish()
})
// /platform-publish/tasks
export const CreatePublishTaskSchema = z.object({
  publishType: z.string(),
  tasks: z.array(z.any()),
  publishChannel: z.enum(['local', 'cloud']).optional(),
}).merge(clientIdSchema)



// 视频 tasks
export const CreateVideoTaskSchema = z.object({
  taskId: z.string(),
  publishData: z.object({
    video: z.object({
        url: z.string().url(),
    }),
    covers: z.array(z.object({
        pathOrUrl: z.string().url(),
    }))
  }),
  authorInfo: z.object({
    id: z.string(),
  }),
  title: z.string().optional(),
  desc: z.string().optional()
})

export const CrawlerWebhookSchema = z.object({
  event: z.string(),
})

export const PublishTaskCallbackSchema = z.object({
  taskId: z.string(),
  stages: z.string(),
  errorMessage: z.string().optional(),
  stageStatus: z.string()
}).merge(CrawlerWebhookSchema)

// 获取发布记录详情的输入schema
export const GetPublishRecordDetailSchema = z.object({
  publishId: z.string()
})

export type CreatePublishTaskInput = z.infer<typeof CreatePublishTaskSchema>
export type CreateVideoTaskInput = z.infer<typeof CreateVideoTaskSchema>
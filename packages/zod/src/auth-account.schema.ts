import { z } from 'zod'
import { dateRangeSchema, PaginationSchema } from './common'

// 平台代码枚举

// KuaiShou: 快手

// XiaoHongShu: 小红书

// DouYin: 抖音

// ShiPinHao: 视频号

// BiLiBiLi: 哔哩哔哩

// BaiJiaHao: 百家号

// TouTiaoHao: 头条号

// XinLangWeiBo: 新浪微博

// XiGuaShiPin: 西瓜视频

// ZhiHu: 知乎

// QiEHao: 企鹅号

// SouHuHao: 搜狐号

// YiDianHao: 一点号

// DaYuHao: 大鱼号

// WangYiHao: 网易号

// AiQiYi: 爱奇艺

// TengXunWeiShi: 腾讯微视

// WeiXinGongZhongHao: 微信公众号

// SouHuShiPin: 搜狐视频

// PiPiXia: 皮皮虾

// TengXunShiPin: 腾讯视频

// DuoDuoShiPin: 多多视频

// AZhan: A站，ACFun

// MeiPai: 美拍

// KuaiChuanHao: 快传号

// XueQiuHao: 雪球号
export const PlatformCodeEnum = z.enum([
  'KuaiShou',
  'XiaoHongShu',
  'DouYin',
  'ShiPinHao',
  'BiLiBiLi',
  'BaiJiaHao',
  'TouTiaoHao',
  'XinLangWeiBo',
  'XiGuaShiPin',
  'ZhiHu',
  'QiEHao',
  'SouHuHao',
  'YiDianHao',
  'DaYuHao',
  'WangYiHao',
  'AiQiYi',
  'TengXunWeiShi',
  'WeiXinGongZhongHao',
  'SouHuShiPin',
  'PiPiXia',
  'TengXunShiPin',
  'DuoDuoShiPin',
  'AZhan',
  'MeiPai',
  'KuaiChuanHao',
  'XueQiuHao',
])

// 授权类型枚举
export const AccountAuthTypeEnum = z.enum(['COOKIE', 'TOKEN'])

const accountSchema = z.object({
  platformUserName: z.string().min(1, '平台用户名不能为空').describe('平台用户名'),
  platformAvatar: z.string().optional().describe('平台用户头像'),
  platformCookie: z.string().min(1, '平台cookie不能为空').describe('平台cookie，支持浏览器格式、JSON对象或JSON数组等字符串形式'),
})

export const AccountBaseSchema = z.object({
  platformCode: PlatformCodeEnum.describe('平台代码'),
  platformUserId: z.string().min(1, '平台用户ID不能为空').describe('平台用户ID'),
})

// 媒体账号绑定schema (OpenAPI用)
export const BindAccountSchema =AccountBaseSchema.merge(accountSchema)



// 媒体账号绑定响应schema
export const BindAccountResponseSchema = z.object({
  id: z.string().describe('数据id'),
})

// 自定义请求头
export const requestHeader = z.object({
  'x-secret-key': z.string().describe('加密密钥，用于加密Cookie'),
})

export const crawlerRequestHeader = requestHeader.extend({
  'x-account-id': z.string().describe('账号ID'),
})

// 媒体账号列表查询参数
export const AccountListQuerySchema = PaginationSchema.merge(dateRangeSchema).extend({
  platformCode: PlatformCodeEnum.optional(),
  search: z.string().optional(), // 搜索用户名或用户ID
})

export const adminAccountListQuerySchema = AccountListQuerySchema.and(
        z.object({
          applicationId: z.string().optional(),
        }),
      )

// 媒体账号列表响应schema
export const MediaAccountResponseSchema = 
  z.object({
    id: z.string().describe('id'),
    platformCode: PlatformCodeEnum.describe('平台代码'),
    platformUserId: z.string().describe('平台用户ID'),
    platformUserName: z.string().describe('平台用户名'),
    platformAvatar: z.string().nullable().describe('平台用户头像'),
    applicationId: z.string().describe('应用ID'),
  })

// 获取账号详情输入schema
export const GetAccountByIdSchema = z.object({
  id: z.string().describe('账号ID'),
})

// 删除账号输入schema
export const DeleteAccountInputSchema = z.object({
  id: z.string().describe('要删除的账号ID'),
})

// 删除账号参数schema
export const DeleteAccountSchema = z.object({
  id: z.string().min(1, '账号ID不能为空'),
})

export const UpdateAccountSchema = AccountBaseSchema.extend({
  id: z.string().describe('账号ID'),
  platformUserName: z.string().min(1, '平台用户名不能为空').optional().describe('平台用户名'),
  platformAvatar: z.string().optional().describe('平台用户头像'),
  platformCookie: z.string().min(1, '平台cookie不能为空').optional().describe('平台cookie，支持浏览器格式、JSON对象或JSON数组等字符串形式'),
})

// 类型推导
export type PlatformCode = z.infer<typeof PlatformCodeEnum>
export type BindAccountInput = z.infer<typeof BindAccountSchema>
export type AccountListQuery = z.infer<typeof AccountListQuerySchema>
export type DeleteAccountInput = z.infer<typeof DeleteAccountSchema>
export type AdminAccountListQuery = z.infer<typeof adminAccountListQuerySchema>
 export const platformOptions: { value: PlatformCode; label: string }[] = [
    { value: 'KuaiShou', label: '快手' },
    { value: 'XiaoHongShu', label: '小红书' },
    { value: 'DouYin', label: '抖音' },
    { value: 'ShiPinHao', label: '视频号' },
    { value: 'BiLiBiLi', label: '哔哩哔哩' },
    { value: 'BaiJiaHao', label: '百家号' },
    { value: 'TouTiaoHao', label: '头条号' },
    { value: 'XinLangWeiBo', label: '新浪微博' },
    { value: 'XiGuaShiPin', label: '西瓜视频' },
    { value: 'ZhiHu', label: '知乎' },
    { value: 'QiEHao', label: '企鹅号' },
    { value: 'SouHuHao', label: '搜狐号' },
    { value: 'YiDianHao', label: '一点号' },
    { value: 'DaYuHao', label: '大鱼号' },
    { value: 'WangYiHao', label: '网易号' },
    { value: 'AiQiYi', label: '爱奇艺' },
    { value: 'TengXunWeiShi', label: '腾讯微视' },
    { value: 'WeiXinGongZhongHao', label: '微信公众号' },
    { value: 'SouHuShiPin', label: '搜狐视频' },
    { value: 'PiPiXia', label: '皮皮虾' },
    { value: 'TengXunShiPin', label: '腾讯视频' },
    { value: 'DuoDuoShiPin', label: '多多视频' },
    { value: 'AZhan', label: 'A站' },
    { value: 'MeiPai', label: '美拍' },
    { value: 'KuaiChuanHao', label: '快传号' },
    { value: 'XueQiuHao', label: '雪球号' }
  ]
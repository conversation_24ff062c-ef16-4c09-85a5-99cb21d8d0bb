{"name": "@coozf/zod", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./src/index.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"zod": "catalog:"}, "devDependencies": {"@coozf/eslint-config": "workspace:*", "@coozf/prettier-config": "workspace:*", "@coozf/tsconfig": "workspace:*"}, "prettier": "@coozf/prettier-config"}
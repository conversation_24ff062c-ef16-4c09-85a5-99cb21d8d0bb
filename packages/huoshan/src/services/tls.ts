import { tlsOpenapi } from '@volcengine/openapi'
import crypto from 'crypto'
import { env } from '../env'

export interface GenerateSLSDto {
  Time: number
  Contents: { Key: string; Value: any }[]
}

export interface TlsDTO {
  logData: string
  requestUri: string
  jobStatus: string
  logLevel: string
}

export interface LogEntry {
  level: 'info' | 'error' | 'warn' | 'debug'
  message: string
  timestamp?: string
  [key: string]: any
}

export class TlsService {
  private tlsOpenapiService = tlsOpenapi.defaultService

  constructor() {
    this.tlsOpenapiService.setSecretKey(env.TLS_SECRET_ACCESS_KEY)
    this.tlsOpenapiService.setAccessKeyId(env.TLS_ACCESS_KEY_ID)
    this.tlsOpenapiService.setHost(env.TLS_HOST)
    this.tlsOpenapiService.setRegion(env.TLS_REGION)
  }

  private generateRequestId(): string {
    return crypto.randomBytes(8).toString('hex')
  }

  async info(message: string, context?: Record<string, any>) {
    await this.log({ level: 'info', message, ...context })
  }

  async error(message: string, context?: Record<string, any>) {
    await this.log({ level: 'error', message, ...context })
  }

  async warn(message: string, context?: Record<string, any>) {
    await this.log({ level: 'warn', message, ...context })
  }

  async debug(message: string, context?: Record<string, any>) {
    await this.log({ level: 'debug', message, ...context })
  }

  private async log(entry: LogEntry) {
    const { level, message, timestamp, ...rest } = entry
    const logEntry = {
      logLevel: level,
      message: message,
      timestamp: timestamp || new Date().toISOString(),
      requestId: rest.requestId || this.generateRequestId(),
      ...rest,
    }

    try {
      const logsBuffer = await tlsOpenapi.TlsService.objToProtoBuffer({
        LogGroups: [
          {
            Logs: [this.dtoToGenerateSLSDto(logEntry)],
            Source: '',
            LogTags: [],
            FileName: '',
            ContextFlow: '',
          },
        ],
      })

      const param = {
        TopicId: env.TLS_LOG_TOPIC_ID,
        LogGroupList: Buffer.from(logsBuffer),
      }

      this.tlsOpenapiService.PutLogs(param)
    } catch (error) {
      // 静默处理TLS日志发送失败，避免影响业务逻辑
      // 可以考虑添加本地日志记录
    }
  }

  private dtoToGenerateSLSDto(slsDto: Record<string, any> = {}): GenerateSLSDto {
    return {
      Time: Math.floor(Date.now() / 1000),
      Contents: Object.entries(slsDto)
        .filter(([_, value]) => value !== undefined && value !== null)
        .map(([key, value]) => ({ 
          Key: key, 
          Value: this.serializeValue(value)
        })),
    }
  }

  private serializeValue(value: any): string {
    try {
      // 如果是原始类型，直接转换
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        return String(value)
      }
      // 如果是对象或数组，进行JSON序列化
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 0)
      }
      // 其他情况回退到String转换
      return String(value)
    } catch (error) {
      // JSON序列化失败时的回退处理
      return String(value)
    }
  }

  async putLogs(context: TlsDTO) {
    const logsBuffer = await tlsOpenapi.TlsService.objToProtoBuffer({
      LogGroups: [
        {
          Logs: [
            {
              Time: Math.floor(Date.now() / 1000),
              Contents: [
                { Key: 'timestamp', Value: new Date().toISOString() },
                ...(context ? Object.entries(context).map(([k, v]) => ({ Key: k, Value: JSON.stringify(v) })) : []),
              ],
            },
          ],
          Source: '',
          LogTags: [],
          FileName: '',
          ContextFlow: '',
        },
      ],
    })
    try {
      await this.tlsOpenapiService.PutLogs({
        TopicId: env.TLS_LOG_TOPIC_ID,
        LogGroupList: Buffer.from(logsBuffer),
      })
    } catch (error) {
      // 静默处理TLS日志发送失败，避免影响业务逻辑
      // 可以考虑添加本地日志记录
    }
  }
}

// 单例模式
export const tlsService = new TlsService()

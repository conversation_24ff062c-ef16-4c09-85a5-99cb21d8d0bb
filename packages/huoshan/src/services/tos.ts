import { TosClient, TosClientError, TosServerError } from '@volcengine/tos-sdk'
import axios from 'axios'
import { PassThrough } from 'stream'
import { env } from '../env'

export class TosService {
  private client: TosClient
  private bucketName = env.OSS_DEFAULT_BUCKET
  private host = `https://${env.OSS_DEFAULT_BUCKET}.tos-cn-shanghai.volces.com`
  private region = 'cn-shanghai'
  private endpoint = 'tos-cn-shanghai.volces.com'

  constructor() {
    this.client = new TosClient({
      accessKeyId: env.OSS_ACCESS_KEY_ID,
      accessKeySecret: env.OSS_ACCESS_KEY_SECRET,
      region: this.region,
      endpoint: this.endpoint
    })
  }

  /**
   * 上传文件
   * @param buffer
   * @param name
   * @returns
   */
  async uploadFile(buffer: Buffer, name: string, bucketName?: string) {
    try {
      await this.client.putObject({
        bucket: bucketName ?? this.bucketName,
        key: name,
        body: buffer
      })
      return name
    } catch (error) {
      throw new Error(`上传文件失败: ${error}`)
    }
  }

  /**
   * 获取资源访问地址
   * @param objectName
   * @param expires
   * @param queries
   * @param bucketName
   * @returns
   */
  async getAccessSignatureUrl(
    objectName: string,
    expires = 1800,
    queries?: Record<string, any>,
    bucketName?: string
  ) {
    try {
      const url = this.client.getPreSignedUrl({
        method: 'GET',
        expires: expires,
        bucket: bucketName ?? this.bucketName,
        key: objectName,
        query: queries
      })

      return url
    } catch (error) {
      throw new Error(`获取访问地址失败: ${error}`)
    }
  }

  /**
   * 获取资源元数据地址
   * @param key
   * @param bucketName
   * @returns
   */
  async getHeadSignatureUrl(key: string, bucketName?: string): Promise<string> {
    try {
      const url = this.client.getPreSignedUrl({
        // @ts-ignore 没有包含HEAD的定义
        method: 'HEAD',
        expires: 300,
        bucket: bucketName ?? this.bucketName,
        key: key
      })

      return url
    } catch (error) {
      if (error instanceof TosClientError) {
        throw new Error(`Client Err Msg:${error.message}, Client Err Stack:${error.stack}`)
      } else if (error instanceof TosServerError) {
        throw new Error(
          `Request ID:${error.requestId}, Response Status Code:${error.statusCode}, Response Err Code:${error.code}, Response Err Msg:${error.message}`
        )
      } else {
        throw new Error(`获取元数据地址失败: ${error}`)
      }
    }
  }

  /**
   * 获取资源直传地址
   * @param objectName
   * @param checksum
   * @param bucketName
   * @returns
   */
  async getUploadSignatureUrl(
    objectName: string,
    checksum?: string | undefined,
    bucketName?: string
  ) {
    try {
      let query = {}
      if (checksum) {
        query = {
          'x-tos-meta-checksum': checksum
        }
      }
      const res = this.client.getPreSignedUrl({
        bucket: bucketName ?? this.bucketName,
        key: objectName,
        method: 'PUT',
        expires: 60 * 30,
        query: query
      })
      return res
    } catch (error) {
      throw new Error(`获取上传地址失败: ${error}`)
    }
  }

  async putObjectByStream(url: string, referer: string, teamCode: string) {
    try {
      const response = await axios.get(url, {
        responseType: 'stream',
        headers: {
          Referer: referer
        }
      })

      const passThrough = new PassThrough()
      response.data.pipe(passThrough)

      const fileName = this.getFileNameFromUrl(url)

      const result = await this.client.putObject({
        bucket: this.bucketName,
        key: `${teamCode}/${fileName}`,
        body: passThrough
      })

      if (result.statusCode === 200) {
        return `${this.host}/${teamCode}/${fileName}`
      }

      return ''
    } catch (error) {
      throw new Error(`流式上传失败: ${error}`)
    }
  }

  /**
   * 获取文件列表
   * @param prefix
   * @param bucketName
   * @returns
   */
  async getFileList(prefix: string, bucketName?: string) {
    try {
      const result = await this.client.listObjectsType2({
        bucket: bucketName ?? this.bucketName,
        prefix: prefix,
        delimiter: '/',
        maxKeys: 50
      })
      return result
    } catch (error) {
      throw new Error(`获取文件列表失败: ${error}`)
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await this.client.deleteObject({ bucket: this.bucketName, key: key })
    } catch (error) {
      throw new Error(`删除文件失败: ${error}`)
    }
  }

  /**
   * 批量删除OSS指定文件
   * @param keys
   */
  async deleteMultiOssObject(keys: string[]) {
    try {
      const objects = keys.map((item) => ({
        key: item
      }))
      await this.client.deleteMultiObjects({ bucket: this.bucketName, objects: objects })
    } catch (error) {
      throw new Error(`批量删除文件失败: ${error}`)
    }
  }

  /**
   * 获取资源的元数据
   * @param key
   */
  async headFileInfo(key: string) {
    try {
      const result = await this.client.headObject({ bucket: this.bucketName, key: key })
      if (result.statusCode === 200) {
        return result.headers['content-length']
      }
      return null
    } catch (error) {
      throw new Error(`获取文件信息失败: ${error}`)
    }
  }

  /**
   * app的apk包下载必须使用自定义域名
   * @param bucketName
   * @param key
   * @param expires
   * @returns
   */
  async getDesktopDownloadUrl(bucketName: string, key: string, expires = 10) {
    try {
      const desktopClient = new TosClient({
        accessKeyId: env.OSS_ACCESS_KEY_ID,
        accessKeySecret: env.OSS_ACCESS_KEY_SECRET,
        region: this.region,
        endpoint: 'yixiaoer.cn'
      })

      const url = desktopClient.getPreSignedUrl({
        method: 'GET',
        expires: expires,
        bucket: bucketName ?? this.bucketName,
        key: key
      })

      return url
    } catch (error) {
      throw new Error(`获取桌面下载地址失败: ${error}`)
    }
  }

  getFileNameFromUrl(url: string): string {
    const parsedUrl = new URL(url)
    const { pathname } = parsedUrl
    if (!pathname) {
      return ''
    }
    const pathWithoutQuery = pathname.split('?')[0]
    if (!pathWithoutQuery) {
      return ''
    }
    const segments = pathWithoutQuery.split('/')
    const fileName = segments[segments.length - 1]
    return fileName || ''
  }
}

// 单例模式
export const tosService = new TosService()
import { FastifyRequest } from 'fastify'
import { tlsService } from '../services/tls'

/**
 * 日志业务逻辑层
 */
export class LoggerService {
  /**
   * 记录信息级别日志
   */
  async info(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    // 提取请求信息并合并到上下文
    const context = this.extractRequestContext(req, extra)
    return tlsService.info(message, context)
  }

  /**
   * 记录错误级别日志
   */
  async error(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    const context = this.extractRequestContext(req, extra)
    return tlsService.error(message, context)
  }

  /**
   * 记录警告级别日志
   */
  async warn(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    const context = this.extractRequestContext(req, extra)
    return tlsService.warn(message, context)
  }

  /**
   * 记录调试级别日志
   */
  async debug(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    const context = this.extractRequestContext(req, extra)
    return tlsService.debug(message, context)
  }

  /**
   * 记录自定义日志
   */
  async putLogs(context: {
    logData: string
    requestUri: string
    jobStatus: string
    logLevel: string
  }) {
    return tlsService.putLogs(context)
  }

  /**
   * 从请求中提取上下文信息
   */
  private extractRequestContext(req: FastifyRequest | null, extra?: Record<string, any>): Record<string, any> {
    const baseContext: Record<string, any> = {}
    
    if (req) {
      baseContext.requestId = req.headers['x-request-id']
      baseContext.method = req.method
      baseContext.url = req.url
      baseContext.userAgent = req.headers['user-agent']
      baseContext.ip = req.ip
    }
    
    return { ...baseContext, ...extra }
  }
}

export const loggerService = new LoggerService()
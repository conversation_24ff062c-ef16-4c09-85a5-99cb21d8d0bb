{"permissions": {"allow": ["Bash(git add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm check-types:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(pnpm dev:client:*)", "Bash(pnpm build:*)", "Bash(find:*)", "Bash(pnpm add:*)", "Bash(pnpm --filter @coozf/ui build)", "Bash(pnpm --filter client check-types)", "Bash(node:*)", "Bash(ls:*)", "WebFetch(domain:github.com)", "Bash(grep:*)", "Bash(pnpm dev:*)", "Bash(pnpm -F admin check-types)", "Bash(pnpm -F admin lint)", "Bash(pnpm generate:*)", "Bash(pnpm push:*)", "Bash(pnpm -F server check-types)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "Bash(pnpm install:*)", "Bash(npx tsx:*)", "Bash(pnpm exec:*)", "Bash(pnpm run:*)", "Bash(pnpm -F client check-types)", "mcp__ide__getDiagnostics", "Bash(git checkout:*)", "Bash(pnpm -F server run build)", "Bash(git fetch:*)", "Bash(git rebase:*)", "Bash(git stash:*)", "Bash(git push:*)", "Bash(git reset:*)", "Bash(git merge-base:*)", "Bash(pnpm pm2:dev:*)", "Bash(pnpm pm2:logs:*)", "Bash(pnpm pm2:stop:*)", "Bash(pm2 delete:*)", "Bash(wc:*)", "Bash(pnpm lint:*)"], "deny": []}}
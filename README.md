<h1 align="center"><strong>Open tRPC - 全栈 SaaS 应用</strong></h1>
<h3 align="center">基于 <a href="https://fastify.io">Fastify</a>、<a href="https://trpc.io">tRPC</a> 和 <a href="https://reactjs.org">React 19</a> 构建的现代化全栈应用</h3>

<p align="center">
  <em>企业级全栈 SaaS 解决方案，支持多租户、实时通信、第三方集成等企业级功能</em>
</p>

## 主要技术栈

| 技术                                              | 描述                                               | Stars                                                                                                                                                                          |
| ------------------------------------------------- | ------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| <a href="https://www.prisma.io/">Prisma</a>      | 现代化的 TypeScript ORM 和数据库工具包              | <a href="https://github.com/prisma/prisma"><img src="https://img.shields.io/github/stars/prisma/prisma?style=flat-square" alt="GitHub Repo stars" /></a>                    |
| <a href="https://fastify.io">Fastify</a>          | 快速、低开销的 Node.js Web 框架                    | <a href="https://github.com/fastify/fastify"><img src="https://img.shields.io/github/stars/fastify/fastify?style=flat-square" alt="GitHub Repo stars" /></a>                   |
| <a href="https://www.mysql.com/">MySQL</a>        | 世界上最流行的开源关系型数据库                      | <a href="https://github.com/mysql/mysql-server"><img src="https://img.shields.io/github/stars/mysql/mysql-server?style=flat-square" alt="GitHub Repo stars" /></a>            |
| <a href="https://reactjs.org">React 19</a>        | 用于构建用户界面的 JavaScript 库                   | <a href="https://github.com/facebook/react"><img src="https://img.shields.io/github/stars/facebook/react?style=flat-square" alt="GitHub Repo stars" /></a>                     |
| <a href="https://tailwindcss.com">Tailwind v4</a> | 实用优先的 CSS 框架，用于快速 UI 开发              | <a href="https://github.com/tailwindlabs/tailwindcss"><img src="https://img.shields.io/github/stars/tailwindlabs/tailwindcss?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://trpc.io">tRPC</a>                | 端到端类型安全的 API 开发框架                      | <a href="https://github.com/trpc/trpc"><img src="https://img.shields.io/github/stars/trpc/trpc?style=flat-square" alt="GitHub Repo stars" /></a>                               |

## 其他核心依赖

| 技术                                                                         | 描述                                               | Stars                                                                                                                                                                        |
| ------------------------------------------------------------------------------- | ------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| <a href="https://better-auth.com">Better Auth</a>                               | 现代化的 Node.js 认证库                           | <a href="https://github.com/better-auth/better-auth"><img src="https://img.shields.io/github/stars/better-auth/better-auth?style=flat-square" alt="GitHub Repo stars" /></a> |
| <a href="https://tanstack.com/router">TanStack Router</a>                       | React 的类型安全路由器                            | <a href="https://github.com/tanstack/router"><img src="https://img.shields.io/github/stars/tanstack/router?style=flat-square" alt="GitHub Repo stars" /></a>                |
| <a href="https://pnpm.io/workspaces">pnpm Workspaces</a>                        | 高效的包管理器和工作空间解决方案                    | <a href="https://github.com/pnpm/pnpm"><img src="https://img.shields.io/github/stars/pnpm/pnpm?style=flat-square" alt="GitHub Repo stars" /></a>                           |
| <a href="https://socket.io">Socket.IO</a>                                       | 实时双向事件通信库                                | <a href="https://github.com/socketio/socket.io"><img src="https://img.shields.io/github/stars/socketio/socket.io?style=flat-square" alt="GitHub Repo stars" /></a>          |
| <a href="https://www.typescriptlang.org">TypeScript</a>                         | JavaScript 的类型安全超集                         | <a href="https://github.com/microsoft/TypeScript"><img src="https://img.shields.io/github/stars/microsoft/TypeScript?style=flat-square" alt="GitHub Repo stars" /></a>       |
| <a href="https://vitejs.dev">Vite</a>                                           | 下一代前端构建工具                                | <a href="https://github.com/vitejs/vite"><img src="https://img.shields.io/github/stars/vitejs/vite?style=flat-square" alt="GitHub Repo stars" /></a>                         |
| <a href="https://zod.dev">Zod</a>                                               | TypeScript 优先的模式验证库                       | <a href="https://github.com/colinhacks/zod"><img src="https://img.shields.io/github/stars/colinhacks/zod?style=flat-square" alt="GitHub Repo stars" /></a>                   |
| <a href="https://redis.io">Redis</a>                                            | 内存数据结构存储，用于缓存和消息队列                | <a href="https://github.com/redis/redis"><img src="https://img.shields.io/github/stars/redis/redis?style=flat-square" alt="GitHub Repo stars" /></a>                        |
| <a href="https://turbo.build">Turbo</a>                                         | 高性能构建系统和任务编排器                         | <a href="https://github.com/vercel/turbo"><img src="https://img.shields.io/github/stars/vercel/turbo?style=flat-square" alt="GitHub Repo stars" /></a>                       |

## 核心功能

### 🔐 认证与授权
- **多种认证方式**: 邮箱/密码、手机号验证、OAuth 社交登录
- **双重认证**: 管理员用户支持 2FA
- **会话管理**: 基于 Better Auth 的现代化会话处理
- **权限控制**: 精细化的用户权限管理系统

### 📊 应用管理
- **应用 CRUD**: 完整的应用创建、编辑、删除功能
- **配额管理**: 灵活的配额分配和使用监控
- **版本控制**: 应用版本管理和更新机制
- **统计分析**: 详细的使用数据统计和图表展示

### 🔌 第三方集成
- **OAuth2 集成**: 支持多个 OAuth 提供商
- **Webhook 系统**: 完整的 Webhook 接收和处理机制
- **消息队列**: 基于 Redis 的异步任务处理
- **文件上传**: OSS 对象存储集成

### 🌐 实时通信
- **Socket.IO**: WebSocket 实时双向通信
- **设备管理**: 位置定位和设备状态管理
- **消息推送**: 实时消息通知系统

### 🛠 开发工具
- **tRPC Panel**: API 调试和测试面板
- **Prisma Studio**: 数据库可视化管理
- **OpenAPI 文档**: 自动生成的 API 文档
- **类型安全**: 端到端的 TypeScript 类型检查

## 快速开始

### 环境要求
- Node.js >= 20.0.0
- MySQL 数据库
- Redis (用于缓存和消息队列)
- pnpm (推荐的包管理器)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd open-trpc
```

2. **安装依赖**
```bash
pnpm install
```

3. **配置环境变量**
```bash
# 复制环境变量模板并配置
cp .env.example .env
# 根据需要修改数据库连接、Redis 连接等配置
```

4. **数据库设置**
```bash
# 推送数据库模式
pnpm push

# 生成 Prisma 客户端
pnpm generate
```

5. **启动开发服务器**
```bash
# 启动所有服务（客户端、服务器、管理后台）
pnpm dev

# 或者单独启动
pnpm dev:client   # 客户端 (http://localhost:5173)
pnpm dev:server   # 服务器 (http://localhost:3000)  
pnpm dev:admin    # 管理后台 (http://localhost:3001)
```

## 生产环境部署

### 构建应用

```bash
# 构建所有应用
pnpm build

# 单独构建
pnpm build:client  # 构建客户端
pnpm build:server  # 构建服务器
pnpm build:admin   # 构建管理后台
```

### PM2 部署

项目支持使用 PM2 进行生产环境部署：

```bash
# 生产环境部署
pnpm -F server pm2:prod

# 预生产环境部署
pnpm -F server pm2:pre

# 开发环境部署
pnpm -F server pm2:dev

# 进程管理
pnpm -F server pm2:stop     # 停止服务
pnpm -F server pm2:restart  # 重启服务
pnpm -F server pm2:logs     # 查看日志
pnpm -F server pm2:monit    # 监控进程
```

### 环境配置

- 确保生产环境的 `.env` 文件配置正确
- 数据库连接、Redis 连接等配置已就绪
- 必要的端口已开放 (默认 3000 和 3001)

## 常用命令

```bash
# 开发相关
pnpm dev            # 启动所有开发服务
pnpm dev:client     # 仅启动客户端
pnpm dev:server     # 仅启动服务器
pnpm dev:admin      # 仅启动管理后台

# 构建相关
pnpm build          # 构建所有应用
pnpm clean          # 清理构建产物

# 数据库相关
pnpm generate       # 生成 Prisma 客户端
pnpm push           # 推送数据库模式
pnpm studio         # 打开 Prisma Studio

# 代码质量
pnpm check-types    # 类型检查
turbo run lint      # 代码检查
```

## 项目架构

### 工作空间结构
- **apps/client**: React 19 前端应用
- **apps/server**: Fastify 后端服务
- **apps/admin**: 管理后台应用
- **packages/db**: 数据库层 (Prisma)
- **packages/auth**: 认证包 (Better Auth)
- **packages/ui**: 共享 UI 组件库
- **tooling/**: 共享工具配置

### 技术特色
- **类型安全**: 端到端 TypeScript 类型检查
- **模块化**: 清晰的分层架构和职责分离
- **现代化**: 使用最新的技术栈和最佳实践
- **可扩展**: 支持水平扩展和微服务架构
- **实时通信**: WebSocket 和消息队列支持

## 开发指南

### 开发工具
- **tRPC Panel**: 访问 `/panel` 进行 API 调试
- **Prisma Studio**: 使用 `pnpm studio` 管理数据库
- **类型检查**: `pnpm check-types` 进行类型验证

### 代码规范
- 遵循 ESLint 和 Prettier 配置
- 使用 TypeScript 严格模式
- 遵守 Clean Architecture 原则

### 测试
目前项目尚未配置完整的测试环境。推荐的测试策略：
- 单元测试：业务逻辑层测试
- 集成测试：tRPC 程序测试
- E2E 测试：关键用户流程测试

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 ISC 许可证。详见 LICENSE 文件。

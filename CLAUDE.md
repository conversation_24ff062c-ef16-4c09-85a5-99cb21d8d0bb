# CLAUDE.md

Always respond in 中文

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

思考与分析规则
需求分析阶段

当用户提出编程需求时，首先进行需求分析和思路梳理
在此阶段不要直接输出代码
重点关注：

理解用户的真实需求
分析技术方案的可行性
提出实现思路和架构设计
识别潜在的技术难点和解决方案

## 需求分析

[描述对需求的理解]

## 技术方案

[提出具体的实现思路]

## 实现步骤

[分解为清晰的实现步骤]

## 注意事项

[标识潜在问题和解决建议]

## Project Structure

This is a fullstack SaaS application built with pnpm workspaces using tRPC for type-safe APIs. The project follows a monorepo structure with clear separation between client, server, and shared packages.

### Workspace Architecture

- **apps/client**: React 19 frontend using Vite, TanStack Router, and Tailwind CSS v4
- **apps/server**: Fastify backend with tRPC, Prisma ORM, and MySQL database
- **apps/admin**: Admin dashboard (similar structure to client)
- **packages/db**: Shared database layer with Prisma schema and repositories
- **packages/auth**: Authentication package using Better Auth
- **packages/ui**: Shared UI components built with shadcn/ui
- **tooling/**: Shared tooling configurations (ESLint, TypeScript, Prettier)

## Common Commands

### Development

```bash
# Start all applications in development mode
pnpm dev

# Start specific applications
pnpm dev:client    # Client frontend
pnpm dev:server    # Server backend
pnpm dev:admin     # Admin dashboard
```

### Building

```bash
# Build all applications
pnpm build

# Build specific applications
pnpm build:client
pnpm build:server
```

### Database Operations

```bash
# Generate Prisma client
pnpm generate

# Push schema changes to database
pnpm push

# Open Prisma Studio
pnpm studio
```

### Quality Assurance

```bash
# Type checking across all packages
pnpm check-types

# Lint all packages (check turbo.json for task configuration)
turbo run lint

# Lint specific application
pnpm -F client lint
pnpm -F server lint
```

### Production

```bash
# Start production server
pnpm start

# Copy frontend build to server public directory
pnpm build:copy-frontend

# Clean all builds and dependencies
pnpm clean
```

### PM2 Deployment (Server)

```bash
# Start in development mode
pnpm -F server pm2:dev

# Start in production mode
pnpm -F server pm2:prod

# Start in pre-production mode
pnpm -F server pm2:pre

# Control PM2 processes
pnpm -F server pm2:stop
pnpm -F server pm2:restart
pnpm -F server pm2:reload
pnpm -F server pm2:delete

# Monitor PM2 processes
pnpm -F server pm2:logs
pnpm -F server pm2:monit
```

## tRPC Configuration

The application uses tRPC for end-to-end type safety with a dual-router setup:

### Server Setup (apps/server/src/)

- **trpc.ts**: Core tRPC instance with superjson transformer and custom error formatting
- **router/index.ts**: Main app router and separate openApiRouter for public APIs
- **procedure/**: Contains different procedure types (public, protected, openProtected)
- **context.ts**: Request context setup

### Procedure Middleware System

The `apps/server/src/procedure/` directory contains reusable middleware for common authentication and authorization patterns:

#### Authentication Middleware (`protected.ts`)
- `protectedProcedure`: Basic user authentication validation
- `adminProtectedProcedure`: Admin user authentication validation

#### Application Permission Middleware (`application.ts`)
- `applicationProcedure`: Validates user owns the specified application
- `applicationWithQuotaProcedure`: Includes quota information with application validation
- `adminApplicationProcedure`: Admin access to any application

#### Open API Middleware (`openProtected.ts`)
- `openAPIProcedure`: Public API authentication via Bearer token
- Utility functions for token validation and platform user verification

#### Usage Benefits
- **Code Reuse**: Eliminates repetitive permission checks across routes
- **Type Safety**: Automatically enhances context with validated data
- **Layered Security**: Progressive permission validation from auth to resource access
- **Consistent Errors**: Unified error handling and messaging

#### Example Usage
```typescript
// Instead of manual permission checking
export const someEndpoint = protectedProcedure
  .input(z.object({ applicationId: z.string() }))
  .query(async ({ input, ctx }) => {
    // Manual app ownership validation needed
  })

// Use application middleware for automatic validation
export const someEndpoint = applicationProcedure
  .input(z.object({ applicationId: z.string().cuid() }))
  .query(async ({ input, ctx }) => {
    // ctx.applicationId and ctx.application are automatically available
    // No manual validation needed
  })
```

### Client Setup (apps/client/src/lib/trpc.ts)

- TanStack Query integration with custom error handling
- Automatic auth token management and logout on 401 errors
- Retry logic for different error types
- Toast notifications for errors

### Development Tools

- **tRPC Panel**: Available at `/panel` endpoint for API testing
- **OpenAPI Documentation**: Auto-generated docs for public APIs
- **Prisma Studio**: Database management interface via `pnpm studio`

### Key Router Modules

- `auth`: Authentication endpoints
- `user`: User management
- `application`: Application CRUD operations
- `quota`: Quota management
- `order`: Order processing
- `oss`: File upload and OSS management
- `openAuth`: Public authentication APIs

## Database Schema

Uses Prisma ORM with MySQL. Key models:

- **User/Admin_user**: User management with dual admin system
- **Application**: Core application entities with quota management
- **AuthAccount**: OAuth integrations for multiple platforms
- **Order**: Purchase and quota management
- **Transaction**: Financial transaction tracking
- **ApiCall**: API usage monitoring

## Authentication

Uses Better Auth package with session-based authentication. Supports:

- Email/password authentication
- OAuth providers
- Phone number verification
- Two-factor authentication (admin users)

## File Structure Conventions

- Use workspace references (e.g., `@coozf/ui`, `@coozf/db`) for internal packages
- Shared configurations in tooling/ directory
- Environment-specific configs use catalog: references in pnpm-workspace.yaml

## Environment Configuration

The application uses environment-specific configurations:

- **Development**: Server runs on ports 3000 (app) and 3001 (admin)
- **Environment Variables**: Configure in `.env` files per workspace
- **Database**: MySQL with Prisma ORM
- **Cache**: Redis for sessions and verification codes

## Code Architecture (lib目录结构)

The server's lib directory has been refactored using clean architecture principles:

### 📁 Directory Structure

```
src/lib/
├── business/          # 业务逻辑层
│   ├── application.ts # 应用管理业务逻辑
│   ├── auth.ts       # 认证业务逻辑
│   ├── quota.ts      # 配额业务逻辑
│   └── order.ts      # 订单业务逻辑
├── services/         # 外部服务层
│   ├── sms.ts       # 短信服务
│   ├── oauth.ts     # OAuth集成
│   ├── cache.ts     # Redis缓存服务
│   ├── webhook.ts   # Webhook通知
│   └── session.ts   # 会话和Token管理
├── utils/           # 纯工具函数
│   ├── crypto.ts    # 加密解密工具
│   ├── token.ts     # JWT工具
│   ├── response.ts  # API响应格式化
│   └── validation.ts # 验证相关工具
└── types/           # 类型定义
    ├── common.ts    # 通用类型
    ├── auth.ts      # 认证相关类型
    ├── quota.ts     # 配额相关类型
    └── index.ts     # 统一导出
```

### 🎯 架构原则

- **职责分离**: 业务逻辑、外部服务、工具函数分层管理
- **依赖注入**: 服务间通过接口解耦，方便测试和替换
- **类型安全**: 完整的TypeScript类型定义
- **向后兼容**: 通过index.ts统一导出，简化迁移

### 📝 使用示例

```typescript
// 推荐的导入方式
import { ApplicationService, QuotaService } from '@/lib/business'
import { smsService, tokenService } from '@/lib/services'
import { generateSecret, ApiError } from '@/lib/utils'
import type { QuotaOverview, SafeUser } from '@/lib/types'

// 或使用统一导入
import { ApplicationService, smsService, generateSecret } from '@/lib'
```

### 🔧 Technical Guidelines

- The project uses pnpm workspaces with catalog dependencies for version management
- TypeScript strict mode is enabled across all packages
- ESLint and Prettier configurations are shared via tooling packages
- Uses Turbo for build caching and task orchestration
- Frontend is designed to be deployed as static files (not SEO-optimized)
- Server code follows clean architecture with clear separation of concerns

## Testing

This project currently does not have a comprehensive testing setup. For future testing implementation, consider:

- Unit tests for business logic in `apps/server/src/lib/business/`
- Integration tests for tRPC procedures
- E2E tests for critical user flows
- API testing using the tRPC Panel or OpenAPI endpoints

## Common Debugging

- **Server logs**: Check console output or PM2 logs for detailed error information
- **Database inspection**: Use `pnpm studio` to inspect database state
- **API testing**: Use tRPC Panel at `/panel` endpoint for testing procedures
- **Type checking**: Run `pnpm check-types` to identify TypeScript issues

## Deployment Scripts

The project includes PM2 ecosystem configuration for production deployment:

```bash
# PM2 scripts are defined in apps/server/ecosystem.config.cjs
pnpm -F server pm2:dev    # Development environment
pnpm -F server pm2:prod   # Production environment
pnpm -F server pm2:pre    # Pre-production environment
```

## Additional Features

This project includes several advanced features:

- **Socket.IO Integration**: Real-time communication support with WebSocket connections
- **Device Management**: Location-based device tracking and management system
- **Message Queue**: Redis-based message publishing and subscribing system
- **OAuth2 Integration**: Multiple OAuth provider support for third-party authentication
- **Webhook System**: Comprehensive webhook handling for external service integration
- **Media Account Management**: Support for managing multiple social media platform accounts
- **File Upload**: OSS (Object Storage Service) integration for file management
- **Crawler Proxy**: Built-in proxy system for web crawling operations
- **Session Management**: Advanced session handling with token refresh scheduling

## OSS File Management

The project uses Volcengine TOS (Object Storage Service) for file management with multiple bucket configuration:

### Bucket Configuration

- **Default Bucket**: `lingjing` (configured via `OSS_DEFAULT_BUCKET` env var)
- **Desktop Bucket**: `yixiaoer-lite-desktop-download` (for version management, via `OSS_DESKTOP_BUCKET` env var)

## Webhook Integration

The project supports webhook integrations for various platforms, with documentation available in:

- `docs/webhook-integration-guide.md`
- `docs/五大平台Webhook功能对接文档.md`

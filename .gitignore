# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# nitro
.nitro/
.output/

# expo
.expo/
expo-env.d.ts
apps/expo/.gitignore
apps/expo/ios
apps/expo/android

# production
build

apps/server/public
apps/server/admin-public

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
dist/
.cache

# turbo
.turbo

generated